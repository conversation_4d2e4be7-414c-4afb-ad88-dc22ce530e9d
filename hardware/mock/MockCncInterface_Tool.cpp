#include <chrono>
#include <cstring>  // for strncpy
#include <filesystem>
#include <fstream>
#include <iomanip>
#include <iostream>
#include <map>
#include <nlohmann/json.hpp>
#include <random>
#include <sstream>
#include <string>
#include <vector>

#include "IAppLogger.h"
#include "MockCncInterface.h"

using json = nlohmann::json;

// 刀具数据文件版本号
constexpr const char* TOOL_DATA_VERSION = "1.1";

// --- 刀具管理 与 刀库管理 (高级接口) ---
ErrorCode MockCncInterface::getCurrentToolInfo(int channelId, ToolInfo& toolInfo) {
    std::lock_guard<std::mutex> lock(m_mutex);
    ErrorCode ec = validateChannel(channelId, "getCurrentToolInfo");
    if (ec != ErrorCode::Success) {
        toolInfo = {};  // 清空输出
        toolInfo.isValid = false;
        return ec;
    }
    toolInfo = channel_current_tool_info_[channelId];
    if (toolInfo.number <= 0) {
        toolInfo.isValid = false;
    }
    return ErrorCode::Success;
}

ErrorCode MockCncInterface::getToolParametersNoLock(int toolNumber, ToolInfo& toolInfo) {
    if (!isInitialized()) {
        toolInfo = {};
        toolInfo.isValid = false;
        return ErrorCode::NotInitialized;
    }
    if (toolNumber <= 0) {
        m_lastError = "getToolParameters 错误: 无效的刀具号 " + std::to_string(toolNumber) + "。";
        toolInfo = {};
        toolInfo.isValid = false;
        return ErrorCode::InvalidParam;
    }

    // 查找刀号对应的D1刀具（主刀具）
    auto range = m_toolNumberToUuidIndex.equal_range(toolNumber);
    for (auto it = range.first; it != range.second; ++it) {
        auto toolIt = m_toolParametersByUuid.find(it->second);
        if (toolIt != m_toolParametersByUuid.end() && toolIt->second.dNumber == 1) {
            toolInfo = toolIt->second;
            toolInfo.isValid = true;
            return ErrorCode::Success;
        }
    }

    m_lastError = "getToolParameters 错误: 未找到刀具号 " + std::to_string(toolNumber) + " 的参数。";
    toolInfo = {};
    toolInfo.number = toolNumber;
    toolInfo.isValid = false;
    return ErrorCode::OperationFailed;
}

ErrorCode MockCncInterface::getToolInfoInPocket(int toolChangerId, int pocketNumber, ToolInfo& toolInfo) {
    std::lock_guard<std::mutex> lock(m_mutex);
    toolInfo = {};
    toolInfo.isValid = false;

    if (toolChangerId < 0 || static_cast<size_t>(toolChangerId) >= system_config_.toolChangersConfigs.size()) {
        m_lastError = "无效的刀库ID " + std::to_string(toolChangerId);
        return ErrorCode::InvalidParam;
    }
    const auto& tcConfig = system_config_.toolChangersConfigs[toolChangerId];
    if (pocketNumber <= 0 || pocketNumber > tcConfig.capacity) {
        m_lastError = "无效的刀位号 " + std::to_string(pocketNumber) + " 对于刀库 " + std::to_string(toolChangerId);
        return ErrorCode::InvalidParam;
    }

    // 在 m_toolParametersByUuid 中查找位于指定刀库和刀位的刀具
    for (const auto& [uuid, tool] : m_toolParametersByUuid) {
        if (tool.toolChangerId == toolChangerId && tool.pocket == pocketNumber && tool.isValid) {
            toolInfo = tool;
            return ErrorCode::Success;
        }
    }

    // 如果没有找到刀具，表示刀位为空，这是正常情况
    return ErrorCode::Success;  // 即使是空刀位，操作本身也是成功的
}

ErrorCode MockCncInterface::loadToolIntoPocket(int toolChangerId, int pocketNumber, int toolNumber) {
    std::lock_guard<std::mutex> lock(m_mutex);
    if (!isInitialized()) return ErrorCode::NotInitialized;

    ToolInfo tempToolInfo;
    if (getToolParametersNoLock(toolNumber, tempToolInfo) != ErrorCode::Success || !tempToolInfo.isValid) {
        m_lastError = "尝试加载未定义的刀具号 " + std::to_string(toolNumber);
        return ErrorCode::InvalidParam;  // 或者 OperationFailed / FileNotFound
    }

    if (toolChangerId < 0 || static_cast<size_t>(toolChangerId) >= system_config_.toolChangersConfigs.size()) {
        m_lastError = "无效的刀库ID " + std::to_string(toolChangerId);
        return ErrorCode::InvalidParam;
    }
    const auto& tcConfig = system_config_.toolChangersConfigs[toolChangerId];
    if (pocketNumber <= 0 || pocketNumber > tcConfig.capacity) {
        m_lastError = "无效的刀位号 " + std::to_string(pocketNumber) + " 对于刀库 " + std::to_string(toolChangerId);
        return ErrorCode::InvalidParam;
    }

    // 检查刀位是否已被其他刀具占用
    for (const auto& [uuid, tool] : m_toolParametersByUuid) {
        if (tool.toolChangerId == toolChangerId && tool.pocket == pocketNumber && tool.isValid &&
            tool.number != toolNumber) {
            m_lastError =
                "刀位 " + std::to_string(pocketNumber) + " 已被刀具 " + std::to_string(tool.number) + " 占用。";
            return ErrorCode::InvalidState;  // 或 OperationFailed
        }
    }

    // 更新刀具的位置信息（使用UUID存储）
    std::string targetUuid;
    auto range = m_toolNumberToUuidIndex.equal_range(toolNumber);
    for (auto it = range.first; it != range.second; ++it) {
        auto toolIt = m_toolParametersByUuid.find(it->second);
        if (toolIt != m_toolParametersByUuid.end() && toolIt->second.dNumber == 1) {
            targetUuid = it->second;
            break;
        }
    }
    if (!targetUuid.empty()) {
        m_toolParametersByUuid[targetUuid].toolChangerId = toolChangerId;
        m_toolParametersByUuid[targetUuid].pocket = pocketNumber;
    }

    // 自动保存刀具数据到文件
    ErrorCode saveResult = saveToolDataToFileNoLock();
    if (saveResult != ErrorCode::Success) {
        HW_LOG_WARN(m_logger, "刀具加载后自动保存失败，但操作已完成");
    }

    return ErrorCode::Success;
}

ErrorCode MockCncInterface::unloadToolFromPocket(int toolChangerId, int pocketNumber, int& unloadedToolNumber) {
    std::lock_guard<std::mutex> lock(m_mutex);
    unloadedToolNumber = 0;
    if (!isInitialized()) return ErrorCode::NotInitialized;

    if (toolChangerId < 0 || static_cast<size_t>(toolChangerId) >= system_config_.toolChangersConfigs.size()) {
        m_lastError = "无效的刀库ID " + std::to_string(toolChangerId);
        return ErrorCode::InvalidParam;
    }
    const auto& tcConfig = system_config_.toolChangersConfigs[toolChangerId];
    if (pocketNumber <= 0 || pocketNumber > tcConfig.capacity) {
        m_lastError = "无效的刀位号 " + std::to_string(pocketNumber) + " 对于刀库 " + std::to_string(toolChangerId);
        return ErrorCode::InvalidParam;
    }

    // 在 m_toolParametersByUuid 中查找位于指定刀库和刀位的刀具
    for (auto& [uuid, tool] : m_toolParametersByUuid) {
        if (tool.toolChangerId == toolChangerId && tool.pocket == pocketNumber && tool.isValid) {
            unloadedToolNumber = tool.number;
            // 将刀具标记为不在刀库中
            tool.toolChangerId = -1;
            tool.pocket = 0;

            // 自动保存刀具数据到文件
            ErrorCode saveResult = saveToolDataToFileNoLock();
            if (saveResult != ErrorCode::Success) {
                HW_LOG_WARN(m_logger, "刀具卸载后自动保存失败，但操作已完成");
            }

            return ErrorCode::Success;
        }
    }

    m_lastError =
        "刀库 " + std::to_string(toolChangerId) + " 刀位 " + std::to_string(pocketNumber) + " 为空或不存在记录。";
    return ErrorCode::OperationFailed;
}

ErrorCode MockCncInterface::exchangeToolsInPockets(int toolChangerId, int pocketNumber1, int pocketNumber2) {
    std::lock_guard<std::mutex> lock(m_mutex);
    if (!isInitialized()) return ErrorCode::NotInitialized;

    if (toolChangerId < 0 || static_cast<size_t>(toolChangerId) >= system_config_.toolChangersConfigs.size()) {
        m_lastError = "无效的刀库ID " + std::to_string(toolChangerId);
        return ErrorCode::InvalidParam;
    }
    const auto& tcConfig = system_config_.toolChangersConfigs[toolChangerId];
    if (pocketNumber1 <= 0 || pocketNumber1 > tcConfig.capacity || pocketNumber2 <= 0 ||
        pocketNumber2 > tcConfig.capacity) {
        m_lastError = "无效的刀位号。";
        return ErrorCode::InvalidParam;
    }
    if (pocketNumber1 == pocketNumber2) return ErrorCode::Success;  // 无需交换

    // 查找两个刀位上的刀具
    std::string uuidInPocket1;
    std::string uuidInPocket2;

    for (const auto& [uuid, tool] : m_toolParametersByUuid) {
        if (tool.toolChangerId == toolChangerId && tool.isValid) {
            if (tool.pocket == pocketNumber1) {
                uuidInPocket1 = uuid;
            } else if (tool.pocket == pocketNumber2) {
                uuidInPocket2 = uuid;
            }
        }
    }

    // 交换两个刀具的位置
    if (!uuidInPocket1.empty()) {
        m_toolParametersByUuid[uuidInPocket1].pocket = pocketNumber2;
    }
    if (!uuidInPocket2.empty()) {
        m_toolParametersByUuid[uuidInPocket2].pocket = pocketNumber1;
    }

    // 自动保存刀具数据到文件
    ErrorCode saveResult = saveToolDataToFileNoLock();
    if (saveResult != ErrorCode::Success) {
        HW_LOG_WARN(m_logger, "刀具位置交换后自动保存失败，但操作已完成");
    } else {
        HW_LOG_DEBUG(m_logger, "刀具位置交换后已自动保存到文件");
    }

    return ErrorCode::Success;
}

ErrorCode MockCncInterface::moveToolFromPocketToSpindle(int channelId, int toolChangerId, int pocketNumber,
                                                        int spindleIndex) {
    std::lock_guard<std::mutex> lock(m_mutex);
    if (!isInitialized()) return ErrorCode::NotInitialized;
    ErrorCode ec = validateChannelAndSpindle(channelId, spindleIndex, "moveToolFromPocketToSpindle");
    if (ec != ErrorCode::Success) return ec;

    if (toolChangerId < 0 || static_cast<size_t>(toolChangerId) >= system_config_.toolChangersConfigs.size()) {
        m_lastError = "无效的刀库ID。";
        return ErrorCode::InvalidParam;
    }
    const auto& tcConfig = system_config_.toolChangersConfigs[toolChangerId];
    if (pocketNumber <= 0 || pocketNumber > tcConfig.capacity) {
        m_lastError = "无效的刀位号。";
        return ErrorCode::InvalidParam;
    }

    if (channel_current_tool_info_[channelId].isValid && channel_current_tool_info_[channelId].number > 0) {
        m_lastError = "通道 " + std::to_string(channelId) + " 主轴 " + std::to_string(spindleIndex) + " 已有刀具。";
        return ErrorCode::InvalidState;
    }

    // 在 m_toolParametersByUuid 中查找位于指定刀库和刀位的刀具
    for (auto& [uuid, tool] : m_toolParametersByUuid) {
        if (tool.toolChangerId == toolChangerId && tool.pocket == pocketNumber && tool.isValid) {
            // 将刀具从刀库移到主轴
            tool.toolChangerId = -1;  // 标记为不在刀库
            tool.pocket = 0;          // 或特定值表示在主轴
            channel_current_tool_info_[channelId] = tool;
            channel_current_tool_info_[channelId].isValid = true;  // 确保在主轴上是有效的

            // 自动保存刀具数据到文件
            ErrorCode saveResult = saveToolDataToFileNoLock();
            if (saveResult != ErrorCode::Success) {
                HW_LOG_WARN(m_logger, "刀具从刀位移动到主轴后自动保存失败，但操作已完成");
            } else {
                HW_LOG_DEBUG(m_logger, "刀具从刀位移动到主轴后已自动保存到文件");
            }

            return ErrorCode::Success;
        }
    }

    // 如果没有找到刀具
    m_lastError = "刀位 " + std::to_string(pocketNumber) + " 为空。";
    return ErrorCode::OperationFailed;
}

ErrorCode MockCncInterface::moveToolFromSpindleToPocket(int channelId, int spindleIndex, int toolChangerId,
                                                        int pocketNumber, int* movedToPocketToolNumber) {
    std::lock_guard<std::mutex> lock(m_mutex);
    if (movedToPocketToolNumber) *movedToPocketToolNumber = 0;
    if (!isInitialized()) return ErrorCode::NotInitialized;
    ErrorCode ec = validateChannelAndSpindle(channelId, spindleIndex, "moveToolFromSpindleToPocket");
    if (ec != ErrorCode::Success) return ec;

    if (toolChangerId < 0 || static_cast<size_t>(toolChangerId) >= system_config_.toolChangersConfigs.size()) {
        m_lastError = "无效的刀库ID。";
        return ErrorCode::InvalidParam;
    }
    const auto& tcConfig = system_config_.toolChangersConfigs[toolChangerId];
    if (pocketNumber <= 0 || pocketNumber > tcConfig.capacity) {
        m_lastError = "无效的刀位号。";
        return ErrorCode::InvalidParam;
    }

    if (!channel_current_tool_info_[channelId].isValid || channel_current_tool_info_[channelId].number <= 0) {
        m_lastError = "通道 " + std::to_string(channelId) + " 主轴 " + std::to_string(spindleIndex) + " 无刀具。";
        return ErrorCode::InvalidState;
    }

    int toolOnSpindle = channel_current_tool_info_[channelId].number;
    std::string spindleToolUuid = channel_current_tool_info_[channelId].uuid;

    // 检查目标刀位是否已被占用
    for (const auto& [uuid, tool] : m_toolParametersByUuid) {
        if (tool.toolChangerId == toolChangerId && tool.pocket == pocketNumber && tool.isValid) {
            m_lastError =
                "刀位 " + std::to_string(pocketNumber) + " 已被刀具 " + std::to_string(tool.number) + " 占用。";
            return ErrorCode::InvalidState;
        }
    }

    // 将主轴上的刀具移动到刀库
    if (!spindleToolUuid.empty() && m_toolParametersByUuid.count(spindleToolUuid)) {
        m_toolParametersByUuid[spindleToolUuid].toolChangerId = toolChangerId;
        m_toolParametersByUuid[spindleToolUuid].pocket = pocketNumber;
        channel_current_tool_info_[channelId] = {};
        channel_current_tool_info_[channelId].isValid = false;
        if (movedToPocketToolNumber) *movedToPocketToolNumber = toolOnSpindle;

        // 自动保存刀具数据到文件
        ErrorCode saveResult = saveToolDataToFileNoLock();
        if (saveResult != ErrorCode::Success) {
            HW_LOG_WARN(m_logger, "刀具从主轴移动到刀位后自动保存失败，但操作已完成");
        } else {
            HW_LOG_DEBUG(m_logger, "刀具从主轴移动到刀位后已自动保存到文件");
        }
    } else {
        m_lastError = "内部错误，未找到主轴刀具参数。";
        return ErrorCode::InternalError;
    }

    return ErrorCode::Success;
}

ErrorCode MockCncInterface::getMagazineStatus(int toolChangerId, std::vector<PocketStatus>& pocketStatuses) {
    std::lock_guard<std::mutex> lock(m_mutex);
    pocketStatuses.clear();
    if (!isInitialized()) return ErrorCode::NotInitialized;

    if (toolChangerId < 0 || static_cast<size_t>(toolChangerId) >= system_config_.toolChangersConfigs.size()) {
        m_lastError = "无效的刀库ID " + std::to_string(toolChangerId);
        return ErrorCode::InvalidParam;
    }

    const auto& tcConfig = system_config_.toolChangersConfigs[toolChangerId];
    pocketStatuses.reserve(tcConfig.capacity);

    // 为每个刀位创建状态
    for (int i = 1; i <= tcConfig.capacity; ++i) {
        PocketStatus ps;
        ps.pocketNumber = i;
        ps.isOccupied = false;
        ps.toolNumberInPocket = 0;

        // 设置刀位状态标志
        // 对于Mock实现，假设所有刀位默认都是启用的
        ps.isEnabled = true;

        // 设置加工位标志：根据刀库类型决定
        // 对于盘式刀库(CAROUSEL)，通常第一个刀位是加工位
        // 对于链式刀库(CHAIN)，可能没有固定的加工位或有特殊的加工位设置
        // 对于手动换刀(MANUAL)，通常没有加工位概念
        switch (tcConfig.type) {
            case ToolChangerType::CAROUSEL:
                ps.isWorkingPosition = (i == 1);  // 盘式刀库第一个位置为加工位
                break;
            case ToolChangerType::CHAIN:
                ps.isWorkingPosition = (i == 1);  // 链式刀库也假设第一个位置为加工位
                break;
            case ToolChangerType::MANUAL:
                ps.isWorkingPosition = false;  // 手动换刀没有固定加工位
                break;
            default:
                ps.isWorkingPosition = (i == 1);  // 默认第一个位置为加工位
                break;
        }

        // 在m_toolParametersByUuid中查找位于该刀位的刀具
        for (const auto& [uuid, tool] : m_toolParametersByUuid) {
            if (tool.toolChangerId == toolChangerId && tool.pocket == i && tool.isValid) {
                ps.isOccupied = true;
                ps.toolNumberInPocket = tool.number;
                break;
            }
        }

        pocketStatuses.push_back(ps);
    }
    return ErrorCode::Success;
}

ErrorCode MockCncInterface::getAllToolParameters(std::map<std::string, ToolInfo>& allToolsInfo) {
    std::lock_guard<std::mutex> lock(m_mutex);
    if (!isInitialized()) {
        return ErrorCode::NotInitialized;
    }

    allToolsInfo.clear();

    // 直接返回所有刀具参数 (基于UUID的新存储)
    // key是UUID，value是刀具信息
    allToolsInfo = m_toolParametersByUuid;

    HW_LOG_INFO(m_logger, "getAllToolParameters: 返回 %zu 个刀具", allToolsInfo.size());
    return ErrorCode::Success;
}

ErrorCode MockCncInterface::getToolsByMagazine(int toolChangerId, std::vector<ToolInfo>& toolsInMagazine) {
    std::lock_guard<std::mutex> lock(m_mutex);
    if (!isInitialized()) {
        return ErrorCode::NotInitialized;
    }

    toolsInMagazine.clear();

    // 验证刀库ID的有效性（除了-1这个特殊值）
    if (toolChangerId != -1) {
        if (toolChangerId < 0 || static_cast<size_t>(toolChangerId) >= system_config_.toolChangersConfigs.size()) {
            m_lastError = "无效的刀库ID " + std::to_string(toolChangerId);
            return ErrorCode::InvalidParam;
        }
    }

    // 遍历所有刀具，筛选指定刀库中的刀具
    for (const auto& [uuid, tool] : m_toolParametersByUuid) {
        if (tool.isValid && tool.toolChangerId == toolChangerId) {
            toolsInMagazine.push_back(tool);
        }
    }

    HW_LOG_INFO(m_logger, "刀库ID=%d, 返回 %zu 个刀具", toolChangerId, toolsInMagazine.size());
    return ErrorCode::Success;
}

// 获取刀具数据文件路径
std::string MockCncInterface::getToolDataFilePath() const {
    if (m_writablePath.empty()) {
        HW_LOG_WARN(m_logger, "writablePath 为空，使用默认刀具数据目录");
        return "./mock_tool_data.json";
    }
    return m_writablePath + "/mock_tool_data.json";
}

// 保存刀具数据到文件
ErrorCode MockCncInterface::saveToolDataToFile() {
    std::lock_guard<std::mutex> lock(m_mutex);
    return saveToolDataToFileNoLock();
}

// 保存刀具数据到文件（不获取锁的版本）
ErrorCode MockCncInterface::saveToolDataToFileNoLock() {
    if (!m_initialized.load()) {
        HW_LOG_ERROR(m_logger, "系统未初始化");
        return ErrorCode::NotInitialized;
    }

    std::string filePath = getToolDataFilePath();

    // 确保目录存在
    std::filesystem::path filePathObj(filePath);
    std::filesystem::path dirPath = filePathObj.parent_path();
    if (!dirPath.empty() && !std::filesystem::exists(dirPath)) {
        try {
            std::filesystem::create_directories(dirPath);
            HW_LOG_INFO(m_logger, "创建刀具数据目录: %s", dirPath.string().c_str());
        } catch (const std::exception& e) {
            HW_LOG_ERROR(m_logger, "无法创建刀具数据目录: %s", e.what());
            return ErrorCode::FileError;
        }
    }

    std::ofstream file(filePath);
    if (!file.is_open()) {
        HW_LOG_ERROR(m_logger, "无法打开刀具数据文件进行写入: %s", filePath.c_str());
        return ErrorCode::FileError;
    }

    try {
        // 创建 JSON 对象
        json toolData;

        // 文件头信息
        toolData["version"] = TOOL_DATA_VERSION;

        // 时间戳
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        std::ostringstream timestamp;
        timestamp << std::put_time(std::localtime(&time_t), "%Y-%m-%dT%H:%M:%S");
        toolData["timestamp"] = timestamp.str();

        // 刀具参数部分 - 使用数组格式与DefaultTools.json一致
        json toolParametersArray = json::array();

        // 从UUID存储中读取所有刀具（包含刀沿）
        for (const auto& [uuid, toolInfo] : m_toolParametersByUuid) {
            if (!toolInfo.isValid) continue;  // 跳过无效刀具

            json toolJson;

            // 基本信息
            toolJson["uuid"] = toolInfo.uuid;
            toolJson["name"] = toolInfo.name;
            toolJson["number"] = toolInfo.number;
            toolJson["toolChangerId"] = toolInfo.toolChangerId;
            toolJson["pocket"] = toolInfo.pocket;
            toolJson["toolTypeCode"] = toolInfo.toolTypeCode;
            toolJson["toolDirection"] = toolInfo.toolDirection;
            toolJson["numberOfFlutes"] = toolInfo.numberOfFlutes;
            toolJson["sisterToolNumber"] = toolInfo.sisterToolNumber;
            toolJson["dNumber"] = toolInfo.dNumber;

            // 几何参数
            toolJson["geometryLengthZ"] = toolInfo.geometryLengthZ;
            toolJson["geometryLengthZWear"] = toolInfo.geometryLengthZWear;
            toolJson["geometryRadius"] = toolInfo.geometryRadius;
            toolJson["geometryRadiusWear"] = toolInfo.geometryRadiusWear;
            toolJson["geometryLengthX"] = toolInfo.geometryLengthX;
            toolJson["geometryLengthXWear"] = toolInfo.geometryLengthXWear;
            toolJson["geometryLengthY"] = toolInfo.geometryLengthY;
            toolJson["geometryLengthYWear"] = toolInfo.geometryLengthYWear;
            toolJson["toolWidth"] = toolInfo.toolWidth;
            toolJson["toolLength"] = toolInfo.toolLength;
            toolJson["cuttingEdgeAngle"] = toolInfo.cuttingEdgeAngle;
            toolJson["tipAngle"] = toolInfo.tipAngle;
            toolJson["noseRadius"] = toolInfo.noseRadius;
            toolJson["activeLengthOffset"] = toolInfo.activeLengthOffset;
            toolJson["activeRadiusOffset"] = toolInfo.activeRadiusOffset;

            // 状态标志
            toolJson["isActive"] = toolInfo.isActive;
            toolJson["isEnabled"] = toolInfo.isEnabled;
            toolJson["isMeasured"] = toolInfo.isMeasured;
            toolJson["isLifeWarningReached"] = toolInfo.isLifeWarningReached;
            toolJson["isChanging"] = toolInfo.isChanging;
            toolJson["isInFixedLocation"] = toolInfo.isInFixedLocation;
            toolJson["wasUsed"] = toolInfo.wasUsed;
            toolJson["coolant1Enabled"] = toolInfo.coolant1Enabled;
            toolJson["coolant2Enabled"] = toolInfo.coolant2Enabled;
            toolJson["isValid"] = toolInfo.isValid;

            // 刀具寿命数据
            json lifeData;
            lifeData["nominalLifeSeconds"] = toolInfo.lifeData.nominalLifeSeconds;
            lifeData["usedLifeSeconds"] = toolInfo.lifeData.usedLifeSeconds;
            lifeData["warningLifeSeconds"] = toolInfo.lifeData.warningLifeSeconds;
            lifeData["nominalUsageCount"] = toolInfo.lifeData.nominalUsageCount;
            lifeData["currentUsageCount"] = toolInfo.lifeData.currentUsageCount;
            lifeData["warningUsageCount"] = toolInfo.lifeData.warningUsageCount;
            lifeData["currentWearValue"] = toolInfo.lifeData.currentWearValue;
            lifeData["maxWearValue"] = toolInfo.lifeData.maxWearValue;
            lifeData["warningWearValue"] = toolInfo.lifeData.warningWearValue;
            toolJson["lifeData"] = lifeData;

            // 主轴方向
            toolJson["spindleDirection"] = static_cast<int>(toolInfo.spindleDirection);

            // 添加到数组
            toolParametersArray.push_back(toolJson);
        }
        toolData["tool_parameters"] = toolParametersArray;

        // 移除刀库内容部分，因为位置信息已经在每个刀具的toolChangerId和pocket字段中
        // 刀库内容可以从m_toolParameters中推导出来，无需单独存储

        // 将 JSON 写入文件，使用缩进格式
        file << toolData.dump(2);
        file.close();

        HW_LOG_INFO(m_logger, "成功保存刀具数据到文件: %s，保存了 %zu 个刀具", filePath.c_str(),
                    m_toolParametersByUuid.size());
        return ErrorCode::Success;

    } catch (const std::exception& e) {
        HW_LOG_ERROR(m_logger, "刀具数据文件写入失败: %s", e.what());
        file.close();
        return ErrorCode::FileError;
    }
}

// 从文件加载刀具数据
ErrorCode MockCncInterface::loadToolDataFromFile() {
    std::lock_guard<std::mutex> lock(m_mutex);
    return loadToolDataFromFileNoLock();
}

// 从文件加载刀具数据（不获取锁的版本）
ErrorCode MockCncInterface::loadToolDataFromFileNoLock() {
    if (!m_initialized.load()) {
        HW_LOG_ERROR(m_logger, "系统未初始化");
        return ErrorCode::NotInitialized;
    }

    std::string filePath = getToolDataFilePath();

    if (!std::filesystem::exists(filePath)) {
        HW_LOG_INFO(m_logger, "刀具数据文件不存在，将使用默认数据: %s", filePath.c_str());
        return ErrorCode::FileNotFound;
    }

    std::ifstream file(filePath);
    if (!file.is_open()) {
        HW_LOG_ERROR(m_logger, "无法打开刀具数据文件进行读取: %s", filePath.c_str());
        return ErrorCode::FileError;
    }

    std::string content((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());
    file.close();

    try {
        // 清空现有数据
        m_toolParametersByUuid.clear();
        m_toolNumberToUuidIndex.clear();

        HW_LOG_INFO(m_logger, "开始解析刀具数据文件: %s", filePath.c_str());

        // 解析 JSON
        json toolData = json::parse(content);

        // 检查版本信息
        bool versionValid = false;

        if (toolData.contains("version")) {
            std::string fileVersion = toolData["version"];
            HW_LOG_INFO(m_logger, "刀具数据文件版本: %s (当前支持版本: %s)", fileVersion.c_str(), TOOL_DATA_VERSION);

            if (fileVersion == TOOL_DATA_VERSION) {
                versionValid = true;
            } else {
                HW_LOG_WARN(m_logger, "刀具数据文件版本不匹配，期望: %s，实际: %s", TOOL_DATA_VERSION,
                            fileVersion.c_str());
            }
        } else {
            HW_LOG_WARN(m_logger, "刀具数据文件缺少版本信息");
        }

        // 如果版本不匹配，重新加载默认刀具数据
        if (!versionValid) {
            HW_LOG_INFO(m_logger, "版本验证失败，开始加载默认刀具数据");

            // 清空现有数据
            m_toolParametersByUuid.clear();
            m_toolNumberToUuidIndex.clear();

            // 加载默认刀具数据
            ErrorCode defaultLoadResult = loadDefaultToolsFromConfigFile();
            if (defaultLoadResult == ErrorCode::Success) {
                HW_LOG_INFO(m_logger, "成功加载默认刀具数据，共 %zu 个刀具", m_toolParametersByUuid.size());

                // 保存默认数据到文件，更新版本号
                ErrorCode saveResult = saveToolDataToFileNoLock();
                if (saveResult == ErrorCode::Success) {
                    HW_LOG_INFO(m_logger, "默认刀具数据已保存到文件");
                } else {
                    HW_LOG_WARN(m_logger, "保存默认刀具数据到文件失败");
                }

                return ErrorCode::Success;
            } else {
                HW_LOG_ERROR(m_logger, "加载默认刀具数据失败，错误代码: %d", static_cast<int>(defaultLoadResult));
                return defaultLoadResult;
            }
        }

        // 解析刀具参数部分
        if (toolData.contains("tool_parameters")) {
            const json& toolParameters = toolData["tool_parameters"];

            for (const auto& toolJson : toolParameters) {
                try {
                    ToolInfo tool;

                    // 基本信息
                    tool.uuid = toolJson.value("uuid", "");     // 重要：从文件加载UUID
                    tool.number = toolJson.value("number", 0);  // 从JSON中读取number字段
                    tool.name = toolJson.value("name", "");
                    tool.toolChangerId = toolJson.value("toolChangerId", -1);
                    tool.pocket = toolJson.value("pocket", 0);
                    tool.toolTypeCode = toolJson.value("toolTypeCode", 0);
                    tool.toolDirection = toolJson.value("toolDirection", 0);
                    tool.numberOfFlutes = toolJson.value("numberOfFlutes", 0);
                    tool.sisterToolNumber = toolJson.value("sisterToolNumber", 1);
                    tool.dNumber = toolJson.value("dNumber", 1);

                    // 几何参数
                    tool.geometryLengthZ = toolJson.value("geometryLengthZ", 0.0);
                    tool.geometryLengthZWear = toolJson.value("geometryLengthZWear", 0.0);
                    tool.geometryRadius = toolJson.value("geometryRadius", 0.0);
                    tool.geometryRadiusWear = toolJson.value("geometryRadiusWear", 0.0);
                    tool.geometryLengthX = toolJson.value("geometryLengthX", 0.0);
                    tool.geometryLengthXWear = toolJson.value("geometryLengthXWear", 0.0);
                    tool.geometryLengthY = toolJson.value("geometryLengthY", 0.0);
                    tool.geometryLengthYWear = toolJson.value("geometryLengthYWear", 0.0);
                    tool.toolWidth = toolJson.value("toolWidth", 0.0);
                    tool.toolLength = toolJson.value("toolLength", 0.0);
                    tool.cuttingEdgeAngle = toolJson.value("cuttingEdgeAngle", 0.0);
                    tool.tipAngle = toolJson.value("tipAngle", 0.0);
                    tool.noseRadius = toolJson.value("noseRadius", 0.0);
                    tool.activeLengthOffset = toolJson.value("activeLengthOffset", 0.0);
                    tool.activeRadiusOffset = toolJson.value("activeRadiusOffset", 0.0);

                    // 状态标志
                    tool.isActive = toolJson.value("isActive", false);
                    tool.isEnabled = toolJson.value("isEnabled", false);
                    tool.isMeasured = toolJson.value("isMeasured", false);
                    tool.isLifeWarningReached = toolJson.value("isLifeWarningReached", false);
                    tool.isChanging = toolJson.value("isChanging", false);
                    tool.isInFixedLocation = toolJson.value("isInFixedLocation", false);
                    tool.wasUsed = toolJson.value("wasUsed", false);
                    tool.coolant1Enabled = toolJson.value("coolant1Enabled", false);
                    tool.coolant2Enabled = toolJson.value("coolant2Enabled", false);
                    tool.isValid = toolJson.value("isValid", false);

                    // 解析刀具寿命数据
                    if (toolJson.contains("lifeData")) {
                        const json& lifeData = toolJson["lifeData"];
                        tool.lifeData.nominalLifeSeconds = lifeData.value("nominalLifeSeconds", 0.0);
                        tool.lifeData.usedLifeSeconds = lifeData.value("usedLifeSeconds", 0.0);
                        tool.lifeData.warningLifeSeconds = lifeData.value("warningLifeSeconds", 0.0);
                        tool.lifeData.nominalUsageCount = lifeData.value("nominalUsageCount", 0);
                        tool.lifeData.currentUsageCount = lifeData.value("currentUsageCount", 0);
                        tool.lifeData.warningUsageCount = lifeData.value("warningUsageCount", 0);
                        tool.lifeData.currentWearValue = lifeData.value("currentWearValue", 0.0);
                        tool.lifeData.maxWearValue = lifeData.value("maxWearValue", 0.0);
                        tool.lifeData.warningWearValue = lifeData.value("warningWearValue", 0.0);
                    }

                    // 解析主轴方向
                    int spindleDir = toolJson.value("spindleDirection", 0);
                    tool.spindleDirection = static_cast<SpindleDirection>(spindleDir);

                    // 确保UUID存在
                    if (tool.uuid.empty()) {
                        tool.uuid = generateUuid();
                    }

                    // 添加到新的UUID存储
                    m_toolParametersByUuid[tool.uuid] = tool;
                    updateToolNumberIndex(tool);

                } catch (const std::exception& e) {
                    HW_LOG_WARN(m_logger, "解析刀具参数时出错: %s", e.what());
                    continue;
                }
            }
        }

        // 移除刀库内容解析部分，因为位置信息已经在每个刀具的toolChangerId和pocket字段中
        // 刀库内容可以从m_toolParameters中推导出来，无需单独加载

        HW_LOG_INFO(m_logger, "成功从文件加载刀具数据: %s，加载了 %zu 个刀具", filePath.c_str(),
                    m_toolParametersByUuid.size());
        return ErrorCode::Success;

    } catch (const json::parse_error& e) {
        HW_LOG_ERROR(m_logger, "刀具数据文件 JSON 解析错误: %s", e.what());
        return ErrorCode::FileError;
    } catch (const std::exception& e) {
        HW_LOG_ERROR(m_logger, "刀具数据文件解析错误: %s", e.what());
        return ErrorCode::FileError;
    }
}

// 获取默认刀具配置文件路径
std::string MockCncInterface::getDefaultToolsFilePath() const {
    if (m_configPath.empty()) {
        HW_LOG_WARN(m_logger, "configPath 为空，无法读取默认刀具文件");
        return "";
    }
    return m_configPath + "/TOOLS/DefaultTools.json";
}

// 从配置文件加载默认刀具数据
ErrorCode MockCncInterface::loadDefaultToolsFromConfigFile() {
    if (!m_initialized.load()) {
        HW_LOG_ERROR(m_logger, "loadDefaultToolsFromConfigFile: 系统未初始化");
        return ErrorCode::NotInitialized;
    }

    std::string filePath = getDefaultToolsFilePath();
    if (filePath.empty()) {
        HW_LOG_WARN(m_logger, "无法确定默认刀具文件路径");
        return ErrorCode::FileNotFound;
    }

    if (!std::filesystem::exists(filePath)) {
        HW_LOG_INFO(m_logger, "默认刀具文件不存在: %s", filePath.c_str());
        return ErrorCode::FileNotFound;
    }

    std::ifstream file(filePath);
    if (!file.is_open()) {
        HW_LOG_ERROR(m_logger, "无法打开默认刀具文件进行读取: %s", filePath.c_str());
        return ErrorCode::FileError;
    }

    std::string content((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());
    file.close();

    try {
        // 解析 JSON
        json toolData = json::parse(content);

        HW_LOG_INFO(m_logger, "开始解析默认刀具配置文件: %s", filePath.c_str());

        // 检查版本信息
        if (toolData.contains("version")) {
            std::string version = toolData["version"];
            HW_LOG_INFO(m_logger, "默认刀具文件版本: %s (当前支持版本: %s)", version.c_str(), TOOL_DATA_VERSION);
        } else {
            HW_LOG_INFO(m_logger, "默认刀具文件缺少版本信息，当前支持版本: %s", TOOL_DATA_VERSION);
        }

        // 解析刀具参数部分
        if (toolData.contains("tool_parameters")) {
            const json& toolParameters = toolData["tool_parameters"];

            for (const auto& toolJson : toolParameters) {
                try {
                    ToolInfo tool;

                    // 基本信息
                    tool.uuid = toolJson.value("uuid", "");     // 重要：从文件加载UUID
                    tool.number = toolJson.value("number", 0);  // 从JSON中读取number字段
                    tool.name = toolJson.value("name", "");
                    tool.toolChangerId = toolJson.value("toolChangerId", -1);
                    tool.pocket = toolJson.value("pocket", 0);
                    tool.toolTypeCode = toolJson.value("toolTypeCode", 0);
                    tool.toolDirection = toolJson.value("toolDirection", 0);
                    tool.numberOfFlutes = toolJson.value("numberOfFlutes", 0);
                    tool.sisterToolNumber = toolJson.value("sisterToolNumber", 1);
                    tool.dNumber = toolJson.value("dNumber", 1);

                    // 几何参数
                    tool.geometryLengthZ = toolJson.value("geometryLengthZ", 0.0);
                    tool.geometryLengthZWear = toolJson.value("geometryLengthZWear", 0.0);
                    tool.geometryRadius = toolJson.value("geometryRadius", 0.0);
                    tool.geometryRadiusWear = toolJson.value("geometryRadiusWear", 0.0);
                    tool.geometryLengthX = toolJson.value("geometryLengthX", 0.0);
                    tool.geometryLengthXWear = toolJson.value("geometryLengthXWear", 0.0);
                    tool.geometryLengthY = toolJson.value("geometryLengthY", 0.0);
                    tool.geometryLengthYWear = toolJson.value("geometryLengthYWear", 0.0);
                    tool.toolWidth = toolJson.value("toolWidth", 0.0);
                    tool.toolLength = toolJson.value("toolLength", 0.0);
                    tool.cuttingEdgeAngle = toolJson.value("cuttingEdgeAngle", 0.0);
                    tool.tipAngle = toolJson.value("tipAngle", 0.0);
                    tool.noseRadius = toolJson.value("noseRadius", 0.0);
                    tool.activeLengthOffset = toolJson.value("activeLengthOffset", 0.0);
                    tool.activeRadiusOffset = toolJson.value("activeRadiusOffset", 0.0);

                    // 状态标志
                    tool.isActive = toolJson.value("isActive", false);
                    tool.isEnabled = toolJson.value("isEnabled", false);
                    tool.isMeasured = toolJson.value("isMeasured", false);
                    tool.isLifeWarningReached = toolJson.value("isLifeWarningReached", false);
                    tool.isChanging = toolJson.value("isChanging", false);
                    tool.isInFixedLocation = toolJson.value("isInFixedLocation", false);
                    tool.wasUsed = toolJson.value("wasUsed", false);
                    tool.coolant1Enabled = toolJson.value("coolant1Enabled", false);
                    tool.coolant2Enabled = toolJson.value("coolant2Enabled", false);
                    tool.isValid = toolJson.value("isValid", true);  // 默认配置文件中的刀具都是有效的

                    // 解析刀具寿命数据
                    if (toolJson.contains("lifeData")) {
                        const json& lifeData = toolJson["lifeData"];
                        tool.lifeData.nominalLifeSeconds = lifeData.value("nominalLifeSeconds", 0.0);
                        tool.lifeData.usedLifeSeconds = lifeData.value("usedLifeSeconds", 0.0);
                        tool.lifeData.warningLifeSeconds = lifeData.value("warningLifeSeconds", 0.0);
                        tool.lifeData.nominalUsageCount = lifeData.value("nominalUsageCount", 0);
                        tool.lifeData.currentUsageCount = lifeData.value("currentUsageCount", 0);
                        tool.lifeData.warningUsageCount = lifeData.value("warningUsageCount", 0);
                        tool.lifeData.currentWearValue = lifeData.value("currentWearValue", 0.0);
                        tool.lifeData.maxWearValue = lifeData.value("maxWearValue", 0.0);
                        tool.lifeData.warningWearValue = lifeData.value("warningWearValue", 0.0);
                    }

                    // 解析主轴方向
                    int spindleDir = toolJson.value("spindleDirection", 0);
                    tool.spindleDirection = static_cast<SpindleDirection>(spindleDir);

                    // 确保UUID存在
                    if (tool.uuid.empty()) {
                        tool.uuid = generateUuid();
                    }

                    // 添加到新的UUID存储
                    m_toolParametersByUuid[tool.uuid] = tool;
                    updateToolNumberIndex(tool);

                    HW_LOG_DEBUG(m_logger, "成功加载默认刀具 %d: %s", tool.number, tool.name.c_str());

                } catch (const std::exception& e) {
                    HW_LOG_WARN(m_logger, "解析默认刀具参数时出错: %s", e.what());
                    continue;
                }
            }
        }

        HW_LOG_INFO(m_logger, "成功从默认配置文件加载刀具数据: %s，加载了 %zu 个刀具", filePath.c_str(),
                    m_toolParametersByUuid.size());
        return ErrorCode::Success;

    } catch (const json::parse_error& e) {
        HW_LOG_ERROR(m_logger, "默认刀具文件 JSON 解析错误: %s", e.what());
        return ErrorCode::FileError;
    } catch (const std::exception& e) {
        HW_LOG_ERROR(m_logger, "默认刀具文件解析错误: %s", e.what());
        return ErrorCode::FileError;
    }
}

// ========== 新的基于 UUID 的刀具管理接口实现 ==========

std::string MockCncInterface::generateUuid() {
    // 简单的 UUID 生成器（基于时间戳和随机数）
    static std::random_device rd;
    static std::mt19937 gen(rd());
    static std::uniform_int_distribution<> dis(0, 15);
    static const char* digits = "0123456789abcdef";

    std::string uuid = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx";
    for (auto& c : uuid) {
        if (c == 'x') {
            c = digits[dis(gen)];
        } else if (c == 'y') {
            c = digits[(dis(gen) & 0x3) | 0x8];
        }
    }
    return uuid;
}

void MockCncInterface::updateToolNumberIndex(const ToolInfo& toolInfo) {
    if (toolInfo.number > 0) {
        // 如果该 UUID 已存在于索引中，先移除
        removeFromToolNumberIndex(toolInfo.uuid);
        // 添加新的映射
        m_toolNumberToUuidIndex.emplace(toolInfo.number, toolInfo.uuid);
    }
}

void MockCncInterface::removeFromToolNumberIndex(const std::string& uuid) {
    // 查找并移除该 UUID 的所有映射
    for (auto it = m_toolNumberToUuidIndex.begin(); it != m_toolNumberToUuidIndex.end();) {
        if (it->second == uuid) {
            it = m_toolNumberToUuidIndex.erase(it);
        } else {
            ++it;
        }
    }
}

ErrorCode MockCncInterface::getToolParameters(const std::string& uuid, ToolInfo& toolInfo) {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!isInitialized()) {
        toolInfo = {};
        toolInfo.isValid = false;
        return ErrorCode::NotInitialized;
    }

    auto it = m_toolParametersByUuid.find(uuid);
    if (it == m_toolParametersByUuid.end()) {
        toolInfo = {};
        toolInfo.isValid = false;
        m_lastError = "getToolParameters 错误: 未找到 UUID " + uuid + " 对应的刀具。";
        return ErrorCode::InvalidParam;
    }

    toolInfo = it->second;
    return ErrorCode::Success;
}

ErrorCode MockCncInterface::setToolParameters(const ToolInfo& toolInfo) {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!isInitialized()) {
        return ErrorCode::NotInitialized;
    }

    // 如果 UUID 为空，说明是创建新刀具
    if (toolInfo.uuid.empty()) {
        toolInfo.uuid = generateUuid();
        HW_LOG_DEBUG(m_logger, "创建新刀具，生成 UUID: %s", toolInfo.uuid.c_str());
    }

    // 验证刀具信息
    if (toolInfo.name.empty()) {
        m_lastError = "setToolParameters 错误: 刀具名称不能为空。";
        return ErrorCode::InvalidParam;
    }

    // 保存到主存储
    m_toolParametersByUuid[toolInfo.uuid] = toolInfo;

    // 更新刀号索引
    updateToolNumberIndex(toolInfo);

    // 自动保存刀具数据到文件
    ErrorCode saveResult = saveToolDataToFileNoLock();
    if (saveResult != ErrorCode::Success) {
        HW_LOG_WARN(m_logger, "刀具参数设置后自动保存失败，但刀具参数已在内存中更新");
    } else {
        HW_LOG_DEBUG(m_logger, "刀具参数设置后已自动保存到文件");
    }

    HW_LOG_DEBUG(m_logger, "设置刀具参数: UUID=%s, 刀号=%d, D%d, 名称=%s", toolInfo.uuid.c_str(), toolInfo.number,
                 toolInfo.dNumber, toolInfo.name.c_str());

    return ErrorCode::Success;
}

ErrorCode MockCncInterface::deleteTool(const std::string& uuid) {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!isInitialized()) {
        return ErrorCode::NotInitialized;
    }

    auto it = m_toolParametersByUuid.find(uuid);
    if (it == m_toolParametersByUuid.end()) {
        m_lastError = "deleteTool 错误: 未找到 UUID " + uuid + " 对应的刀具。";
        return ErrorCode::InvalidParam;
    }

    ToolInfo toolInfo = it->second;

    // 从主存储中移除
    m_toolParametersByUuid.erase(it);

    // 从刀号索引中移除
    removeFromToolNumberIndex(uuid);

    // 自动保存刀具数据到文件
    ErrorCode saveResult = saveToolDataToFileNoLock();
    if (saveResult != ErrorCode::Success) {
        HW_LOG_WARN(m_logger, "刀具删除后自动保存失败，但删除操作已完成");
    } else {
        HW_LOG_DEBUG(m_logger, "刀具删除后已自动保存到文件");
    }

    HW_LOG_DEBUG(m_logger, "删除刀具: UUID=%s, 刀号=%d, D%d", uuid.c_str(), toolInfo.number, toolInfo.dNumber);

    return ErrorCode::Success;
}

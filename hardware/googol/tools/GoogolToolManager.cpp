#include "GoogolToolManager.h"
#include "../GoogolCncInterface.h"
#include <algorithm>
#include <random>
#include <sstream>
#include <iomanip>
#include <fstream>
#include <filesystem>
#include <chrono>
#include <memory>
#include <nlohmann/json.hpp>

using json = nlohmann::json;

// 刀具数据文件版本号
constexpr const char* TOOL_DATA_VERSION = "1.2";

GoogolToolManager::GoogolToolManager(GoogolCncInterface& interface)
    : m_interface(interface), m_isInitialized(false) {
}

ErrorCode GoogolToolManager::initialize() {
    std::lock_guard<std::mutex> lock(m_toolMutex);

    HW_LOG_INFO(m_interface.m_logger, "开始初始化刀具管理器");

    if (!m_interface.isInitialized()) {
        HW_LOG_ERROR(m_interface.m_logger, "CNC接口未初始化，无法初始化刀具管理器");
        return ErrorCode::NotInitialized;
    }

    // 初始化系统刀库
    HW_LOG_DEBUG(m_interface.m_logger, "初始化系统刀库");
    initializeSystemMagazine();

    // 加载刀具数据（从配置文件或默认数据）
    HW_LOG_DEBUG(m_interface.m_logger, "加载刀具数据");
    ErrorCode loadResult = loadToolDataFromConfig();
    if (loadResult != ErrorCode::Success) {
        HW_LOG_WARN(m_interface.m_logger, "从配置文件加载刀具数据失败，使用默认数据");
        // 如果加载失败，使用默认数据
        initializeToolData();
    } else {
        HW_LOG_INFO(m_interface.m_logger, "成功从配置文件加载刀具数据");
    }

    // 初始化H映射表
    HW_LOG_DEBUG(m_interface.m_logger, "初始化H映射表");
    initializeHMappingTable();

    m_isInitialized = true;
    HW_LOG_INFO(m_interface.m_logger, "刀具管理器初始化完成");
    return ErrorCode::Success;
}

ErrorCode GoogolToolManager::getCurrentToolInfo(int channelId, ToolInfo& toolInfo) {
    if (channelId < 0 || channelId >= m_interface.m_loadedChannelsCount) {
        m_interface.m_lastError = "无效的通道ID: " + std::to_string(channelId);
        return ErrorCode::InvalidParam;
    }

    toolInfo = {};  // 初始化
    toolInfo.isValid = false;

    // 从SDK获取当前主轴刀号
    int currentToolNumber = m_interface.m_ncChannelOutPtrArr[channelId].m_shm32Ptr->NC_Status_Mode_Type_T;
    if (currentToolNumber <= 0 || currentToolNumber >= TOOL_NUM_MAX) {
        m_interface.m_lastError = "当前主轴无刀具";
        return ErrorCode::InvalidState;
    }

    // 获取H模态号
    int hNumber = m_interface.m_ncChannelOutPtrArr[channelId].m_shm32Ptr->NC_Status_Mode_Type_H;
    if (hNumber <= 0 || hNumber > 100) {
        m_interface.m_lastError = "当前主轴无有效H号";
        return ErrorCode::InvalidState;
    }

    // 通过H号获取刀具信息
    return getToolInfoByHNumber(hNumber, toolInfo);
}

ErrorCode GoogolToolManager::getToolParameters(const std::string& uuid, ToolInfo& toolInfo) {
    std::lock_guard<std::mutex> lock(m_toolMutex);

    if (!m_isInitialized) {
        return ErrorCode::NotInitialized;
    }

    auto it = m_toolParametersByUuid.find(uuid);
    if (it == m_toolParametersByUuid.end()) {
        m_interface.m_lastError = "getToolParameters 错误: 未找到 UUID " + uuid;
        return ErrorCode::InvalidParam;
    }

    toolInfo = it->second;
    return ErrorCode::Success;
}

ErrorCode GoogolToolManager::setToolParameters(const ToolInfo& toolInfo) {
    std::lock_guard<std::mutex> lock(m_toolMutex);

    if (!m_isInitialized) {
        HW_LOG_ERROR(m_interface.m_logger, "刀具管理器未初始化，无法设置刀具参数");
        return ErrorCode::NotInitialized;
    }

    if (toolInfo.uuid.empty()) {
        HW_LOG_ERROR(m_interface.m_logger, "必须指定UUID");
        m_interface.m_lastError = "必须指定UUID";
        return ErrorCode::InvalidParam;
    }

    // 验证刀具信息
    if (toolInfo.name.empty()) {
        HW_LOG_ERROR(m_interface.m_logger, "刀具名称不能为空");
        m_interface.m_lastError = "错误: 刀具名称不能为空。";
        return ErrorCode::InvalidParam;
    }

    // 验证几何参数
    if (toolInfo.geometryRadius < 0 || toolInfo.geometryLengthZ < 0) {
        HW_LOG_ERROR(m_interface.m_logger, "刀具几何参数不能为负数: 半径=%.3f, 长度=%.3f",
                    toolInfo.geometryRadius, toolInfo.geometryLengthZ);
        m_interface.m_lastError = "错误: 几何参数不能为负数。";
        return ErrorCode::InvalidParam;
    }

    // 保存到主存储
    m_toolParametersByUuid[toolInfo.uuid] = toolInfo;
    HW_LOG_DEBUG(m_interface.m_logger, "刀具参数已保存到内存: T%d 半径=%.3f 长度=%.3f",
                toolInfo.number, toolInfo.geometryRadius, toolInfo.geometryLengthZ);

    // 更新刀号索引
    updateToolNumberIndex(toolInfo);

    // 更新H映射表
    updateHMappingTable();

    // 自动保存到配置文件
    ErrorCode saveResult = saveToolDataToConfigNoLock();
    if (saveResult != ErrorCode::Success) {
        HW_LOG_WARN(m_interface.m_logger, "刀具参数保存到配置文件失败，但内存设置成功");
        // 保存失败不影响设置成功
    } else {
        HW_LOG_DEBUG(m_interface.m_logger, "刀具参数已保存到配置文件");
    }

    return ErrorCode::Success;
}

ErrorCode GoogolToolManager::deleteTool(const std::string& uuid) {
    std::lock_guard<std::mutex> lock(m_toolMutex);

    HW_LOG_DEBUG(m_interface.m_logger, "删除刀具: UUID=%s", uuid.c_str());

    if (!m_isInitialized) {
        HW_LOG_ERROR(m_interface.m_logger, "刀具管理器未初始化，无法删除刀具");
        return ErrorCode::NotInitialized;
    }

    auto it = m_toolParametersByUuid.find(uuid);
    if (it == m_toolParametersByUuid.end()) {
        HW_LOG_ERROR(m_interface.m_logger, "删除刀具失败: 未找到UUID %s", uuid.c_str());
        m_interface.m_lastError = "deleteTool 错误: 未找到 UUID " + uuid;
        return ErrorCode::InvalidParam;
    }

    ToolInfo toolInfo = it->second;
    HW_LOG_INFO(m_interface.m_logger, "删除刀具: T%d %s (UUID: %s)",
               toolInfo.number, toolInfo.name.c_str(), uuid.c_str());

    // 从主存储中删除
    m_toolParametersByUuid.erase(it);

    // 从刀号索引中移除
    removeFromToolNumberIndex(uuid);

    // 更新H映射表
    updateHMappingTable();

    // 自动保存到配置文件
    ErrorCode saveResult = saveToolDataToConfigNoLock();
    if (saveResult != ErrorCode::Success) {
        HW_LOG_WARN(m_interface.m_logger, "刀具删除后保存配置文件失败，但删除操作成功");
        // 保存失败不影响删除成功
    } else {
        HW_LOG_DEBUG(m_interface.m_logger, "刀具删除后配置文件已更新");
    }

    return ErrorCode::Success;
}

ErrorCode GoogolToolManager::getAllToolParameters(std::map<std::string, ToolInfo>& allToolsInfo) {
    std::lock_guard<std::mutex> lock(m_toolMutex);

    if (!m_isInitialized) {
        return ErrorCode::NotInitialized;
    }

    allToolsInfo = m_toolParametersByUuid;

    HW_LOG_DEBUG(m_interface.m_logger, "getAllToolParameters: 返回 %zu 个刀具", allToolsInfo.size());
    return ErrorCode::Success;
}

void GoogolToolManager::initializeHMappingTable() {
    // 注意：此方法假设已经获得了m_toolMutex锁
    
    // 设置H映射表大小（最大100个H号）
    m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20200 - 500] = 100;

    // 清空H映射表
    for (int i = 1; i <= 100; i++) {
        m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20200 + i - 500] = 0;
    }

    // 根据当前刀具列表重建H映射表
    updateHMappingTable();
}

void GoogolToolManager::updateHMappingTable() {
    // 注意：此方法假设已经获得了m_toolMutex锁

    // 清空H映射表
    for (int hIndex = 1; hIndex <= 100; hIndex++) {
        m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20200 + hIndex - 500] = 0;
    }

    // 清空所有通道的ToolPara数组
    for (int channelId = 0; channelId < m_interface.m_loadedChannelsCount; ++channelId) {
        for (int i = 0; i < 100; i++) { // 假设最大100个H号
            TOOL_PARA_PTR& toolPara = m_interface.m_toolsParamPtr[channelId].m_shmPtr->m_ToolPara[i];
            memset(&toolPara, 0, sizeof(toolPara));
        }
    }

    // 清空H号映射缓存
    clearHMappingCache();

    int nextAvailableH = 1; // 下一个可用的H号

    // 遍历所有刀具，按刀号排序建立H映射
    std::map<int, std::vector<ToolInfo>> toolsByNumber;
    for (const auto& [uuid, tool] : m_toolParametersByUuid) {
        if (tool.isValid && tool.number > 0) {
            toolsByNumber[tool.number].push_back(tool);
        }
    }

    // 为每个刀号的每个刀沿分配H号并设置刀补数据
    for (const auto& [toolNumber, tools] : toolsByNumber) {
        // 按刀沿号排序
        std::vector<ToolInfo> sortedTools = tools;
        std::sort(sortedTools.begin(), sortedTools.end(),
                 [](const ToolInfo& a, const ToolInfo& b) {
                     return a.dNumber < b.dNumber;
                 });

        for (const auto& tool : sortedTools) {
            if (nextAvailableH > 100) {
                break;
            }

            // 计算刀号+刀沿的组合值（刀号*100+刀沿）
            int toolNumberAndEdge = toolNumber * 100 + tool.dNumber;

            // 设置H映射：H号 -> 刀号+刀沿
            m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20200 + nextAvailableH - 500] = toolNumberAndEdge;

            // 更新H号映射缓存
            m_hNumberToUuidCache[nextAvailableH] = tool.uuid;
            m_uuidToHNumberCache[tool.uuid] = nextAvailableH;
            m_hNumberToToolNumberAndDCache[nextAvailableH] = toolNumberAndEdge;

            // 将刀补数据保存到对应H号索引的ToolPara中
            int hIndex = nextAvailableH - 1; // ToolPara数组索引从0开始，H号从1开始
            for (int channelId = 0; channelId < m_interface.m_loadedChannelsCount; ++channelId) {
                setToolToDeviceByHIndex(channelId, hIndex, tool);
            }

            nextAvailableH++;
        }
    }

    // 更新H映射表实际大小
    m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20200 - 500] = nextAvailableH - 1;
}

int GoogolToolManager::findHNumberForTool(int toolNumber, int dNumber) {
    std::lock_guard<std::mutex> lock(m_toolMutex);

    HW_LOG_DEBUG(m_interface.m_logger, "查找刀具T%d D%d对应的H号", toolNumber, dNumber);

    // 优先使用缓存进行快速查找
    int targetValue = toolNumber * 100 + dNumber;
    for (const auto& [hNumber, cachedValue] : m_hNumberToToolNumberAndDCache) {
        if (cachedValue == targetValue) {
            HW_LOG_DEBUG(m_interface.m_logger, "从缓存找到T%d D%d -> H%d", toolNumber, dNumber, hNumber);
            return hNumber;
        }
    }

    // 如果缓存中没有找到，回退到宏变量查找（兼容性保证）
    int mappingTableSize = static_cast<int>(m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20200 - 500]);
    HW_LOG_DEBUG(m_interface.m_logger, "缓存中未找到，从宏变量查找，映射表大小: %d", mappingTableSize);

    for (int hNumber = 1; hNumber <= mappingTableSize && hNumber <= 100; hNumber++) {
        int mappedValue = static_cast<int>(m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20200 + hNumber - 500]);
        if (mappedValue == targetValue) {
            HW_LOG_DEBUG(m_interface.m_logger, "从宏变量找到T%d D%d -> H%d", toolNumber, dNumber, hNumber);
            return hNumber;
        }
    }

    HW_LOG_WARN(m_interface.m_logger, "未找到刀具T%d D%d对应的H号", toolNumber, dNumber);
    return 0; // 未找到
}

ErrorCode GoogolToolManager::getToolInfoByHNumber(int hNumber, ToolInfo& toolInfo) {
    std::lock_guard<std::mutex> lock(m_toolMutex);

    if (!m_isInitialized) {
        return ErrorCode::NotInitialized;
    }

    if (hNumber <= 0 || hNumber > 100) {
        m_interface.m_lastError = "无效的H号: " + std::to_string(hNumber);
        return ErrorCode::InvalidParam;
    }

    // 优先使用缓存进行快速查找
    auto cacheIt = m_hNumberToUuidCache.find(hNumber);
    if (cacheIt != m_hNumberToUuidCache.end()) {
        auto toolIt = m_toolParametersByUuid.find(cacheIt->second);
        if (toolIt != m_toolParametersByUuid.end()) {
            toolInfo = toolIt->second;
            return ErrorCode::Success;
        }
    }

    // 如果缓存中没有找到，回退到宏变量查找（兼容性保证）
    int mappedValue = static_cast<int>(m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20200 + hNumber - 500]);
    if (mappedValue <= 0) {
        m_interface.m_lastError = "H" + std::to_string(hNumber) + " 未映射到任何刀具";
        return ErrorCode::InvalidState;
    }

    // 解析刀号和刀沿
    int toolNumber = mappedValue / 100;
    int dNumber = mappedValue % 100;

    // 在刀具列表中查找对应的刀具
    auto range = m_toolNumberToUuidIndex.equal_range(toolNumber);
    for (auto it = range.first; it != range.second; ++it) {
        auto toolIt = m_toolParametersByUuid.find(it->second);
        if (toolIt != m_toolParametersByUuid.end() && 
            toolIt->second.isValid && 
            toolIt->second.dNumber == dNumber) {
            toolInfo = toolIt->second;
            return ErrorCode::Success;
        }
    }

    m_interface.m_lastError = "H" + std::to_string(hNumber) + " 映射的刀具T" + std::to_string(toolNumber) +
                            ".D" + std::to_string(dNumber) + " 在刀具列表中不存在";
    return ErrorCode::InvalidState;
}

ErrorCode GoogolToolManager::getToolCompensationByHNumber(int channelId, int hNumber, ToolInfo& toolInfo) {
    if (channelId < 0 || channelId >= m_interface.m_loadedChannelsCount) {
        m_interface.m_lastError = "无效的通道ID: " + std::to_string(channelId);
        return ErrorCode::InvalidParam;
    }

    if (hNumber <= 0 || hNumber > 100) {
        m_interface.m_lastError = "无效的H号: " + std::to_string(hNumber);
        return ErrorCode::InvalidParam;
    }

    // 从ToolPara数组直接读取刀补数据
    int hIndex = hNumber - 1; // ToolPara数组索引从0开始
    const TOOL_PARA_PTR& toolPara = m_interface.m_toolsParamPtr[channelId].m_shmPtr->m_ToolPara[hIndex];

    // 先获取刀具基本信息
    ErrorCode result = getToolInfoByHNumber(hNumber, toolInfo);
    if (result != ErrorCode::Success) {
        // 如果找不到刀具信息，创建一个基本的刀具信息结构
        toolInfo = {};
        toolInfo.isValid = false;
        toolInfo.number = 0;
        toolInfo.dNumber = 0;
        toolInfo.name = "H" + std::to_string(hNumber) + " 未知刀具";
    }

    // 从ToolPara读取实时刀补数据
    toolInfo.geometryLengthX = toolPara.m_ToolLen[0];
    toolInfo.geometryLengthXWear = toolPara.m_ToolHComp[0];
    toolInfo.geometryLengthY = toolPara.m_ToolLen[1];
    toolInfo.geometryLengthYWear = toolPara.m_ToolHComp[1];
    toolInfo.geometryLengthZ = toolPara.m_ToolLen[2];
    toolInfo.geometryLengthZWear = toolPara.m_ToolHComp[2];
    toolInfo.geometryRadius = toolPara.m_ToolR;
    toolInfo.geometryRadiusWear = toolPara.m_ToolDComp;
    toolInfo.activeLengthOffset = toolPara.m_ToolHComp[2];
    toolInfo.activeRadiusOffset = toolPara.m_ToolDComp;

    return ErrorCode::Success;
}

ErrorCode GoogolToolManager::syncHMappingToMacroVars() {
    std::lock_guard<std::mutex> lock(m_toolMutex);

    if (!m_isInitialized) {
        return ErrorCode::NotInitialized;
    }

    // 重新构建H映射表
    updateHMappingTable();

    return ErrorCode::Success;
}

void GoogolToolManager::initializeSystemMagazine() {
    // 注意：此方法假设已经获得了m_toolMutex锁

    HW_LOG_DEBUG(m_interface.m_logger, "初始化系统刀库");

    // 首先尝试从配置文件加载刀库配置
    ErrorCode configResult = loadMagazineConfigFromFile();
    if (configResult == ErrorCode::Success && !m_systemMagazines.empty()) {
        // 配置文件加载成功，更新宏变量
        auto& mainMagazine = m_systemMagazines.begin()->second;
        HW_LOG_INFO(m_interface.m_logger, "从配置文件加载刀库成功: %s 容量%d",
                   mainMagazine.name.c_str(), mainMagazine.capacity);
        m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20001 - 500] = mainMagazine.capacity;
        return;
    }

    // 配置文件加载失败，从宏变量读取或使用默认配置
    HW_LOG_WARN(m_interface.m_logger, "从配置文件加载刀库失败，使用默认配置");
    m_systemMagazines.clear();

    // 默认刀库参数
    int toolChangerType = 0;
    int totalToolCount = 25;

    // 创建主刀库
    GoogolMagazineInfo mainMagazine;
    mainMagazine.id = 0;
    mainMagazine.name = "主刀库";
    mainMagazine.capacity = totalToolCount;
    mainMagazine.pockets.resize(totalToolCount);
    for (int i = 0; i < totalToolCount; ++i) {
        mainMagazine.pockets[i].isEmpty = true;
        mainMagazine.pockets[i].toolUuid = "";
    }
    m_systemMagazines[0] = mainMagazine;

    HW_LOG_INFO(m_interface.m_logger, "初始化默认主刀库: %s 容量%d",
               mainMagazine.name.c_str(), mainMagazine.capacity);

    // 同步现有刀具信息到刀库
    syncToolsToMagazines();

    // 更新刀库基本参数到宏变量
    m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20000 - 500] = toolChangerType;  // 刀库类型：0=无刀库
    m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20001 - 500] = totalToolCount;  // 刀库总刀数（设置为实际需要的数量）
    m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20002 - 500] = 0;  // 刀库运行状态（初始为0）
    m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20003 - 500] = 0;  // 主轴当前刀号（初始为0）
    m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20004 - 500] = 1;  // 刀库当前位置（初始为1）
    m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20005 - 500] = 0;  // 刀库备刀刀号（初始为0）

    // 换刀位置设置
    m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20006 - 500] = 1;     // X/Y轴换刀移动启动：1=启用
    m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20007 - 500] = 0.0;   // 换刀位置X坐标(mm)
    m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20008 - 500] = 0.0;   // 换刀位置Y坐标(mm)
    m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20009 - 500] = 0.0;   // 换刀位置Z坐标(mm)
    m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20010 - 500] = 6000;  // 换刀快移速度(mm/min)
    m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20011 - 500] = 1000;  // 换刀慢移速度(mm/min)
    m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20027 - 500] = 0.0;   // 换刀后退安全位置X坐标(mm)
    m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20028 - 500] = 0.0;   // 换刀后退安全位置Y坐标(mm)
    m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20029 - 500] = 0.0;   // 换刀后退安全位置Z坐标(mm)

    // 关键屏蔽参数设置（实验环境专用）
    m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20151 - 500] = 3;  // 刀库异常状态：3=屏蔽所有异常
    m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20152 - 500] = 1;  // 屏蔽刀库侧门检测：1=是
    m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20153 - 500] = 1;  // 屏蔽刀库门开关指令：1=是
    m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20154 - 500] = 1;  // 开启运行时备刀完成刀套下：1=是
    m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20155 - 500] = 1;  // 屏蔽探针开启检测：1=是
    m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20156 - 500] = 1;  // 屏蔽刀套下判断刀库门：1=是

    // 刀号对应刀套号映射
    for (int i = 0; i < totalToolCount; i++) {
        m_interface.m_macroPublicParamPtr[0].m_shmPtr->m_dPublicUserVar[20031 + i - 500] = i + 1;
    }

    HW_LOG_INFO(m_interface.m_logger, "初始化系统刀库完成");
}

void GoogolToolManager::initializeToolData() {
    // 注意：此方法假设已经获得了m_toolMutex锁

    // 清空现有数据
    m_toolParametersByUuid.clear();
    m_toolNumberToUuidIndex.clear();
    clearHMappingCache();

    // 添加测试刀具数据
    addTestTools();

    // 重建H映射表
    updateHMappingTable();
}

void GoogolToolManager::addTestTools() {
    // 注意：此方法假设已经获得了m_toolMutex锁

    HW_LOG_INFO(m_interface.m_logger, "开始添加测试刀具数据");

    // 创建测试刀具1：端铣刀
    ToolInfo testTool1;
    testTool1.uuid = generateUuid();
    testTool1.number = 1;
    testTool1.name = "测试端铣刀";
    testTool1.toolTypeCode = 100;  // 铣刀类型代码
    testTool1.geometryRadius = 5.0;  // 半径5mm，直径10mm
    testTool1.geometryLengthZ = 80.0;
    testTool1.toolChangerId = 1;
    testTool1.pocket = 1;
    testTool1.isValid = true;
    testTool1.toolDirection = 1;
    testTool1.numberOfFlutes = 4;
    testTool1.sisterToolNumber = 1;
    testTool1.dNumber = 1;

    // 创建测试刀具2：钻头
    ToolInfo testTool2;
    testTool2.uuid = generateUuid();
    testTool2.number = 2;
    testTool2.name = "测试钻头";
    testTool2.toolTypeCode = 200;  // 钻头类型代码
    testTool2.geometryRadius = 4.0;  // 半径4mm，直径8mm
    testTool2.geometryLengthZ = 100.0;
    testTool2.toolChangerId = 1;
    testTool2.pocket = 2;
    testTool2.isValid = true;
    testTool2.toolDirection = 1;
    testTool2.numberOfFlutes = 2;
    testTool2.sisterToolNumber = 1;
    testTool2.dNumber = 1;

    // 创建测试刀具3：球头铣刀
    ToolInfo testTool3;
    testTool3.uuid = generateUuid();
    testTool3.number = 3;
    testTool3.name = "测试球头铣刀";
    testTool3.toolTypeCode = 110;  // 球头铣刀类型代码
    testTool3.geometryRadius = 3.0;  // 半径3mm，直径6mm
    testTool3.geometryLengthZ = 75.0;
    testTool3.toolChangerId = 1;
    testTool3.pocket = 3;
    testTool3.isValid = true;
    testTool3.toolDirection = 1;
    testTool3.numberOfFlutes = 2;
    testTool3.sisterToolNumber = 1;
    testTool3.dNumber = 1;

    // 添加到刀具列表
    m_toolParametersByUuid[testTool1.uuid] = testTool1;
    m_toolParametersByUuid[testTool2.uuid] = testTool2;
    m_toolParametersByUuid[testTool3.uuid] = testTool3;

    // 更新刀号索引
    updateToolNumberIndex(testTool1);
    updateToolNumberIndex(testTool2);
    updateToolNumberIndex(testTool3);

    HW_LOG_INFO(m_interface.m_logger, "成功添加3个测试刀具: T1端铣刀, T2钻头, T3球头铣刀");
}

void GoogolToolManager::syncToolsToMagazines() {
    HW_LOG_INFO(m_interface.m_logger, "开始同步刀具信息到刀库");

    int syncedToolCount = 0;

    // 遍历所有刀具，将它们同步到对应的刀库刀位
    for (const auto& [uuid, toolInfo] : m_toolParametersByUuid) {
        if (!toolInfo.isValid) {
            continue;
        }

        // 检查刀具是否已分配到刀库
        if (toolInfo.toolChangerId >= 0 && toolInfo.pocket > 0) {
            // 查找对应的刀库
            auto magazineIt = m_systemMagazines.find(toolInfo.toolChangerId);
            if (magazineIt != m_systemMagazines.end()) {
                auto& magazine = magazineIt->second;

                // 检查刀位号是否有效
                if (toolInfo.pocket <= static_cast<int>(magazine.pockets.size())) {
                    int pocketIndex = toolInfo.pocket - 1; // 刀位号从1开始，数组索引从0开始

                    // 更新刀位信息
                    magazine.pockets[pocketIndex].toolUuid = uuid;
                    magazine.pockets[pocketIndex].isEmpty = false;

                    syncedToolCount++;
                    HW_LOG_DEBUG(m_interface.m_logger, "同步刀具 T%d (%s) 到刀库%d刀位%d",
                                toolInfo.number, toolInfo.name.c_str(),
                                toolInfo.toolChangerId, toolInfo.pocket);
                } else {
                    HW_LOG_WARN(m_interface.m_logger, "刀具 T%d 的刀位号%d 超出刀库%d容量%zu",
                               toolInfo.number, toolInfo.pocket,
                               toolInfo.toolChangerId, magazine.pockets.size());
                }
            } else {
                HW_LOG_WARN(m_interface.m_logger, "刀具 T%d 指定的刀库%d 不存在",
                           toolInfo.number, toolInfo.toolChangerId);
            }
        } else {
            HW_LOG_DEBUG(m_interface.m_logger, "刀具 T%d (%s) 未分配到刀库，跳过同步",
                        toolInfo.number, toolInfo.name.c_str());
        }
    }

    HW_LOG_INFO(m_interface.m_logger, "刀具同步完成，共同步了 %d 个刀具到刀库", syncedToolCount);
}

// === 刀库管理实现 ===

ErrorCode GoogolToolManager::getToolInfoInPocket(int toolChangerId, int pocketNumber, ToolInfo& toolInfo) {
    std::lock_guard<std::mutex> lock(m_toolMutex);

    toolInfo = {};
    toolInfo.isValid = false;

    if (!m_isInitialized) {
        return ErrorCode::NotInitialized;
    }

    // 在刀具列表中查找位于指定刀库和刀位的刀具
    for (const auto& [uuid, tool] : m_toolParametersByUuid) {
        if (tool.toolChangerId == toolChangerId && tool.pocket == pocketNumber && tool.isValid) {
            toolInfo = tool;
            return ErrorCode::Success;
        }
    }

    return ErrorCode::InvalidState;
}

ErrorCode GoogolToolManager::loadToolIntoPocket(int toolChangerId, int pocketNumber, int toolNumber) {
    std::lock_guard<std::mutex> lock(m_toolMutex);

    if (!m_isInitialized) {
        return ErrorCode::NotInitialized;
    }

    // 通过刀号索引查找对应的UUID
    std::string targetUuid;
    auto range = m_toolNumberToUuidIndex.equal_range(toolNumber);
    if (range.first == range.second) {
        m_interface.m_lastError = "装载刀具到刀位错误: 未找到刀具号 " + std::to_string(toolNumber) + " 对应的刀具";
        return ErrorCode::InvalidParam;
    }

    // 查找有效的刀具
    ToolInfo tempToolInfo;
    bool foundValidTool = false;
    for (auto it = range.first; it != range.second; ++it) {
        auto toolIt = m_toolParametersByUuid.find(it->second);
        if (toolIt != m_toolParametersByUuid.end() && toolIt->second.isValid) {
            tempToolInfo = toolIt->second;
            targetUuid = it->second;
            foundValidTool = true;
            break;
        }
    }

    if (!foundValidTool) {
        m_interface.m_lastError = "装载刀具到刀位错误: 刀具号 " + std::to_string(toolNumber) + " 无有效刀具";
        return ErrorCode::InvalidParam;
    }

    // 检查刀位是否已被其他刀具占用
    for (const auto& [uuid, tool] : m_toolParametersByUuid) {
        if (tool.toolChangerId == toolChangerId && tool.pocket == pocketNumber && tool.isValid && uuid != targetUuid) {
            m_interface.m_lastError = "装载刀具到刀位错误: 刀位 " + std::to_string(pocketNumber) + " 已被刀具 " +
                          std::to_string(tool.number) + " 占用。";
            return ErrorCode::InvalidState;
        }
    }

    // 更新刀具的位置信息
    m_toolParametersByUuid[targetUuid].toolChangerId = toolChangerId;
    m_toolParametersByUuid[targetUuid].pocket = pocketNumber;

    return ErrorCode::Success;
}

ErrorCode GoogolToolManager::unloadToolFromPocket(int toolChangerId, int pocketNumber, int& unloadedToolNumber) {
    std::lock_guard<std::mutex> lock(m_toolMutex);

    unloadedToolNumber = 0;
    if (!m_isInitialized) {
        return ErrorCode::NotInitialized;
    }

    // 在刀具列表中查找位于指定刀库和刀位的刀具
    for (auto& [uuid, tool] : m_toolParametersByUuid) {
        if (tool.toolChangerId == toolChangerId && tool.pocket == pocketNumber && tool.isValid) {
            unloadedToolNumber = tool.number;
            // 将刀具标记为不在刀库中
            tool.toolChangerId = -1;
            tool.pocket = -1;
            return ErrorCode::Success;
        }
    }

    return ErrorCode::OperationFailed;
}

ErrorCode GoogolToolManager::exchangeToolsInPockets(int toolChangerId, int pocketNumber1, int pocketNumber2) {
    std::lock_guard<std::mutex> lock(m_toolMutex);

    if (!m_isInitialized) {
        return ErrorCode::NotInitialized;
    }

    if (pocketNumber1 == pocketNumber2) {
        return ErrorCode::Success; // 相同刀位，无需交换
    }

    // 查找两个刀位中的刀具
    std::string uuidInPocket1;
    std::string uuidInPocket2;

    for (const auto& [uuid, tool] : m_toolParametersByUuid) {
        if (tool.toolChangerId == toolChangerId && tool.isValid) {
            if (tool.pocket == pocketNumber1) {
                uuidInPocket1 = uuid;
            } else if (tool.pocket == pocketNumber2) {
                uuidInPocket2 = uuid;
            }
        }
    }

    // 交换两个刀具的位置
    if (!uuidInPocket1.empty()) {
        m_toolParametersByUuid[uuidInPocket1].pocket = pocketNumber2;
    }
    if (!uuidInPocket2.empty()) {
        m_toolParametersByUuid[uuidInPocket2].pocket = pocketNumber1;
    }

    return ErrorCode::Success;
}

ErrorCode GoogolToolManager::getToolsByMagazine(int toolChangerId, std::vector<ToolInfo>& toolsInMagazine) {
    std::lock_guard<std::mutex> lock(m_toolMutex);

    if (!m_isInitialized) {
        return ErrorCode::NotInitialized;
    }

    toolsInMagazine.clear();

    // 遍历所有刀具，筛选指定刀库中的刀具
    for (const auto& [uuid, tool] : m_toolParametersByUuid) {
        if (tool.isValid && tool.toolChangerId == toolChangerId) {
            toolsInMagazine.push_back(tool);
        }
    }

    return ErrorCode::Success;
}

// === 内部辅助方法实现 ===

std::string GoogolToolManager::generateUuid() {
    // 简化的UUID生成（实际项目中应使用更好的UUID库）
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 15);

    std::stringstream ss;
    ss << std::hex;
    for (int i = 0; i < 32; ++i) {
        if (i == 8 || i == 12 || i == 16 || i == 20) {
            ss << "-";
        }
        ss << dis(gen);
    }
    return ss.str();
}

void GoogolToolManager::updateToolNumberIndex(const ToolInfo& toolInfo) {
    // 注意：此方法假设已经获得了m_toolMutex锁

    // 先移除旧的索引（如果存在）
    removeFromToolNumberIndex(toolInfo.uuid);

    // 添加新的索引
    if (toolInfo.isValid && toolInfo.number > 0) {
        m_toolNumberToUuidIndex.emplace(toolInfo.number, toolInfo.uuid);
    }
}

void GoogolToolManager::removeFromToolNumberIndex(const std::string& uuid) {
    // 注意：此方法假设已经获得了m_toolMutex锁

    // 查找并移除所有匹配的条目
    for (auto it = m_toolNumberToUuidIndex.begin(); it != m_toolNumberToUuidIndex.end();) {
        if (it->second == uuid) {
            it = m_toolNumberToUuidIndex.erase(it);
        } else {
            ++it;
        }
    }
}

void GoogolToolManager::clearHMappingCache() {
    // 注意：此方法假设已经获得了m_toolMutex锁
    m_hNumberToUuidCache.clear();
    m_uuidToHNumberCache.clear();
    m_hNumberToToolNumberAndDCache.clear();
}

ErrorCode GoogolToolManager::setToolToDeviceByHIndex(int channelId, int hIndex, const ToolInfo& toolInfo) {
    // 刀具信息保存到SDK（按H号索引）
    // hIndex对应ToolPara数组的索引，与H号一一对应
    TOOL_PARA_PTR& toolPara = m_interface.m_toolsParamPtr[channelId].m_shmPtr->m_ToolPara[hIndex];

    // 刀具X轴
    toolPara.m_ToolLen[0] = toolInfo.geometryLengthX;
    toolPara.m_ToolHComp[0] = toolInfo.geometryLengthXWear;

    // 刀具Y轴
    toolPara.m_ToolLen[1] = toolInfo.geometryLengthY;
    toolPara.m_ToolHComp[1] = toolInfo.geometryLengthYWear;

    // 刀具Z轴
    toolPara.m_ToolLen[2] = toolInfo.geometryLengthZ;
    toolPara.m_ToolHComp[2] = toolInfo.geometryLengthZWear;

    // 刀具半径
    toolPara.m_ToolR = toolInfo.geometryRadius;
    toolPara.m_ToolDComp = toolInfo.geometryRadiusWear;

    // TOFF：计算当前激活的补偿值？？
    // toolPara.m_ToolHComp[2] = toolInfo.activeLengthOffset;
    // toolPara.m_ToolDComp = toolInfo.activeRadiusOffset;

    return ErrorCode::Success;
}

// === 配置文件管理实现 ===

ErrorCode GoogolToolManager::loadToolDataFromConfig() {
    // 注意：此方法假设已经获得了m_toolMutex锁

    std::string configPath = getToolDataConfigPath();
    HW_LOG_DEBUG(m_interface.m_logger, "加载刀具数据配置文件: %s", configPath.c_str());

    if (!std::filesystem::exists(configPath)) {
        HW_LOG_INFO(m_interface.m_logger, "用户配置文件不存在，尝试加载默认配置");
        // 尝试从默认配置文件加载
        ErrorCode defaultResult = loadDefaultToolsFromConfigFile();
        if (defaultResult == ErrorCode::Success) {
            HW_LOG_INFO(m_interface.m_logger, "成功加载默认刀具配置，保存到用户配置文件");
            // 保存默认数据到用户配置文件
            ErrorCode saveResult = saveToolDataToConfigNoLock();
            if (saveResult != ErrorCode::Success) {
                HW_LOG_WARN(m_interface.m_logger, "保存默认配置到用户文件失败，但加载成功");
                // 保存失败不影响加载成功
            }
            return ErrorCode::Success;
        }
        HW_LOG_ERROR(m_interface.m_logger, "默认配置文件也不存在或加载失败");
        return ErrorCode::FileNotFound;
    }

    std::ifstream file(configPath);
    if (!file.is_open()) {
        m_interface.m_lastError = "无法打开刀具配置文件: " + configPath;
        return ErrorCode::FileError;
    }

    std::string content((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());
    file.close();

    try {
        // 解析 JSON
        json toolData = json::parse(content);

        // 检查版本兼容性
        bool versionValid = false;
        if (toolData.contains("version")) {
            std::string fileVersion = toolData["version"];
            if (fileVersion == TOOL_DATA_VERSION) {
                versionValid = true;
            }
        }

        // 如果版本不匹配，重新加载默认刀具数据
        if (!versionValid) {
            // 清空现有数据
            m_toolParametersByUuid.clear();
            m_toolNumberToUuidIndex.clear();
            clearHMappingCache();

            // 加载默认刀具数据
            ErrorCode defaultLoadResult = loadDefaultToolsFromConfigFile();
            if (defaultLoadResult == ErrorCode::Success) {
                // 保存默认数据到文件，更新版本号
                ErrorCode saveResult = saveToolDataToConfigNoLock();
                if (saveResult != ErrorCode::Success) {
                    // 保存失败不影响加载成功
                }
                return ErrorCode::Success;
            } else {
                return defaultLoadResult;
            }
        }

        // 清空现有数据
        m_toolParametersByUuid.clear();
        m_toolNumberToUuidIndex.clear();
        clearHMappingCache();

        // 解析刀具参数部分
        if (toolData.contains("tool_parameters")) {
            const json& toolParameters = toolData["tool_parameters"];
            int loadedCount = 0;

            for (const auto& toolJson : toolParameters) {
                try {
                    ToolInfo toolInfo = createToolInfoFromJson(toolJson);

                    if (validateToolInfo(toolInfo)) {
                        m_toolParametersByUuid[toolInfo.uuid] = toolInfo;
                        updateToolNumberIndex(toolInfo);
                        loadedCount++;
                    }
                } catch (const std::exception& e) {
                    // 跳过无效的刀具数据
                    continue;
                }
            }

            HW_LOG_INFO(m_interface.m_logger, "从配置文件加载了 %d 个刀具", loadedCount);
        }

        // 同步刀具信息到刀库
        syncToolsToMagazines();

        return ErrorCode::Success;

    } catch (const json::parse_error& e) {
        m_interface.m_lastError = "解析刀具配置文件失败: " + std::string(e.what());
        return ErrorCode::FileError;
    } catch (const std::exception& e) {
        m_interface.m_lastError = "刀具配置文件解析错误: " + std::string(e.what());
        return ErrorCode::FileError;
    }
}

ErrorCode GoogolToolManager::saveToolDataToConfig() {
    std::lock_guard<std::mutex> lock(m_toolMutex);
    return saveToolDataToConfigNoLock();
}

ErrorCode GoogolToolManager::saveToolDataToConfigNoLock() {
    if (!m_isInitialized) {
        HW_LOG_ERROR(m_interface.m_logger, "刀具管理器未初始化，无法保存配置");
        return ErrorCode::NotInitialized;
    }

    std::string configPath = getToolDataConfigPath();
    HW_LOG_DEBUG(m_interface.m_logger, "保存刀具数据到配置文件: %s", configPath.c_str());

    // 确保目录存在
    std::filesystem::path filePathObj(configPath);
    std::filesystem::path dirPath = filePathObj.parent_path();
    if (!dirPath.empty() && !std::filesystem::exists(dirPath)) {
        try {
            std::filesystem::create_directories(dirPath);
            HW_LOG_DEBUG(m_interface.m_logger, "创建配置目录: %s", dirPath.string().c_str());
        } catch (const std::exception& e) {
            HW_LOG_ERROR(m_interface.m_logger, "无法创建刀具配置目录: %s", e.what());
            m_interface.m_lastError = "无法创建刀具配置目录: " + std::string(e.what());
            return ErrorCode::FileError;
        }
    }

    std::ofstream file(configPath);
    if (!file.is_open()) {
        HW_LOG_ERROR(m_interface.m_logger, "无法写入刀具配置文件: %s", configPath.c_str());
        m_interface.m_lastError = "无法写入刀具配置文件: " + configPath;
        return ErrorCode::FileError;
    }

    try {
        // 创建 JSON 对象
        json toolData;

        // 文件头信息
        toolData["version"] = TOOL_DATA_VERSION;

        // 时间戳
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        std::ostringstream timestamp;
        timestamp << std::put_time(std::localtime(&time_t), "%Y-%m-%dT%H:%M:%S");
        toolData["timestamp"] = timestamp.str();

        // 刀具参数部分
        json toolParametersArray = json::array();

        int validToolCount = 0;
        for (const auto& [uuid, toolInfo] : m_toolParametersByUuid) {
            if (!toolInfo.isValid) continue;  // 跳过无效刀具

            json toolJson = createJsonFromToolInfo(toolInfo);
            toolParametersArray.push_back(toolJson);
            validToolCount++;
        }

        toolData["tool_parameters"] = toolParametersArray;

        // 将 JSON 写入文件，使用缩进格式
        file << toolData.dump(2);
        file.close();

        HW_LOG_INFO(m_interface.m_logger, "成功保存%d个刀具到配置文件: %s", validToolCount, configPath.c_str());
        return ErrorCode::Success;

    } catch (const std::exception& e) {
        HW_LOG_ERROR(m_interface.m_logger, "刀具配置文件写入失败: %s", e.what());
        m_interface.m_lastError = "刀具配置文件写入失败: " + std::string(e.what());
        file.close();
        return ErrorCode::FileError;
    }
}

std::string GoogolToolManager::getToolDataConfigPath() {
    // 用户数据可写目录/TOOLS/ToolData.json
    std::filesystem::path configPath = std::filesystem::path(m_interface.m_writablePath) / "TOOLS" / "ToolData.json";
    return configPath.string();
}

std::string GoogolToolManager::getDefaultToolsFilePath() {
    // 默认刀具配置文件路径
    std::filesystem::path defaultPath = std::filesystem::path(m_interface.m_configPath) / "configs" / "TOOLS" / "DefaultTools.json";
    return defaultPath.string();
}

ToolInfo GoogolToolManager::createToolInfoFromJson(const json& jsonObj) {
    ToolInfo toolInfo;

    // 基本信息
    toolInfo.uuid = jsonObj.value("uuid", "");
    toolInfo.name = jsonObj.value("name", "");
    toolInfo.number = jsonObj.value("number", 0);
    toolInfo.toolChangerId = jsonObj.value("toolChangerId", -1);
    toolInfo.pocket = jsonObj.value("pocket", 0);

    // 刀具描述与分类
    toolInfo.toolTypeCode = jsonObj.value("toolTypeCode", 0);
    toolInfo.toolDirection = jsonObj.value("toolDirection", 1);
    toolInfo.numberOfFlutes = jsonObj.value("numberOfFlutes", 0);
    toolInfo.sisterToolNumber = jsonObj.value("sisterToolNumber", 1);
    toolInfo.dNumber = jsonObj.value("dNumber", 1);

    // 几何参数
    toolInfo.geometryLengthZ = jsonObj.value("geometryLengthZ", 0.0);
    toolInfo.geometryLengthZWear = jsonObj.value("geometryLengthZWear", 0.0);
    toolInfo.geometryRadius = jsonObj.value("geometryRadius", 0.0);
    toolInfo.geometryRadiusWear = jsonObj.value("geometryRadiusWear", 0.0);
    toolInfo.geometryLengthX = jsonObj.value("geometryLengthX", 0.0);
    toolInfo.geometryLengthXWear = jsonObj.value("geometryLengthXWear", 0.0);
    toolInfo.geometryLengthY = jsonObj.value("geometryLengthY", 0.0);
    toolInfo.geometryLengthYWear = jsonObj.value("geometryLengthYWear", 0.0);
    toolInfo.toolWidth = jsonObj.value("toolWidth", 0.0);
    toolInfo.toolLength = jsonObj.value("toolLength", 0.0);
    toolInfo.cuttingEdgeAngle = jsonObj.value("cuttingEdgeAngle", 0.0);
    toolInfo.tipAngle = jsonObj.value("tipAngle", 0.0);
    toolInfo.noseRadius = jsonObj.value("noseRadius", 0.0);

    // 当前激活的补偿值
    toolInfo.activeLengthOffset = jsonObj.value("activeLengthOffset", 0.0);
    toolInfo.activeRadiusOffset = jsonObj.value("activeRadiusOffset", 0.0);

    // 状态标志
    toolInfo.isActive = jsonObj.value("isActive", false);
    toolInfo.isEnabled = jsonObj.value("isEnabled", true);
    toolInfo.isMeasured = jsonObj.value("isMeasured", false);
    toolInfo.isLifeWarningReached = jsonObj.value("isLifeWarningReached", false);
    toolInfo.isChanging = jsonObj.value("isChanging", false);
    toolInfo.isInFixedLocation = jsonObj.value("isInFixedLocation", false);
    toolInfo.wasUsed = jsonObj.value("wasUsed", false);
    toolInfo.isValid = jsonObj.value("isValid", true);

    // 寿命数据
    if (jsonObj.contains("lifeData")) {
        const json& lifeDataObj = jsonObj["lifeData"];
        toolInfo.lifeData.nominalLifeSeconds = lifeDataObj.value("nominalLifeSeconds", 0.0);
        toolInfo.lifeData.usedLifeSeconds = lifeDataObj.value("usedLifeSeconds", 0.0);
        toolInfo.lifeData.warningLifeSeconds = lifeDataObj.value("warningLifeSeconds", 0.0);
        toolInfo.lifeData.nominalUsageCount = lifeDataObj.value("nominalUsageCount", 0);
        toolInfo.lifeData.currentUsageCount = lifeDataObj.value("currentUsageCount", 0);
        toolInfo.lifeData.warningUsageCount = lifeDataObj.value("warningUsageCount", 0);
        toolInfo.lifeData.currentWearValue = lifeDataObj.value("currentWearValue", 0.0);
        toolInfo.lifeData.maxWearValue = lifeDataObj.value("maxWearValue", 0.0);
        toolInfo.lifeData.warningWearValue = lifeDataObj.value("warningWearValue", 0.0);
    }

    // 主轴控制与冷却液设置
    toolInfo.spindleDirection = static_cast<SpindleDirection>(jsonObj.value("spindleDirection", 0));
    toolInfo.coolant1Enabled = jsonObj.value("coolant1Enabled", false);
    toolInfo.coolant2Enabled = jsonObj.value("coolant2Enabled", false);

    return toolInfo;
}

json GoogolToolManager::createJsonFromToolInfo(const ToolInfo& toolInfo) {
    json jsonObj;

    // 基本信息
    jsonObj["uuid"] = toolInfo.uuid;
    jsonObj["name"] = toolInfo.name;
    jsonObj["number"] = toolInfo.number;
    jsonObj["toolChangerId"] = toolInfo.toolChangerId;
    jsonObj["pocket"] = toolInfo.pocket;

    // 刀具描述与分类
    jsonObj["toolTypeCode"] = toolInfo.toolTypeCode;
    jsonObj["toolDirection"] = toolInfo.toolDirection;
    jsonObj["numberOfFlutes"] = toolInfo.numberOfFlutes;
    jsonObj["sisterToolNumber"] = toolInfo.sisterToolNumber;
    jsonObj["dNumber"] = toolInfo.dNumber;

    // 几何参数
    jsonObj["geometryLengthZ"] = toolInfo.geometryLengthZ;
    jsonObj["geometryLengthZWear"] = toolInfo.geometryLengthZWear;
    jsonObj["geometryRadius"] = toolInfo.geometryRadius;
    jsonObj["geometryRadiusWear"] = toolInfo.geometryRadiusWear;
    jsonObj["geometryLengthX"] = toolInfo.geometryLengthX;
    jsonObj["geometryLengthXWear"] = toolInfo.geometryLengthXWear;
    jsonObj["geometryLengthY"] = toolInfo.geometryLengthY;
    jsonObj["geometryLengthYWear"] = toolInfo.geometryLengthYWear;
    jsonObj["toolWidth"] = toolInfo.toolWidth;
    jsonObj["toolLength"] = toolInfo.toolLength;
    jsonObj["cuttingEdgeAngle"] = toolInfo.cuttingEdgeAngle;
    jsonObj["tipAngle"] = toolInfo.tipAngle;
    jsonObj["noseRadius"] = toolInfo.noseRadius;

    // 当前激活的补偿值
    jsonObj["activeLengthOffset"] = toolInfo.activeLengthOffset;
    jsonObj["activeRadiusOffset"] = toolInfo.activeRadiusOffset;

    // 状态标志
    jsonObj["isActive"] = toolInfo.isActive;
    jsonObj["isEnabled"] = toolInfo.isEnabled;
    jsonObj["isMeasured"] = toolInfo.isMeasured;
    jsonObj["isLifeWarningReached"] = toolInfo.isLifeWarningReached;
    jsonObj["isChanging"] = toolInfo.isChanging;
    jsonObj["isInFixedLocation"] = toolInfo.isInFixedLocation;
    jsonObj["wasUsed"] = toolInfo.wasUsed;
    jsonObj["isValid"] = toolInfo.isValid;

    // 寿命数据
    json lifeDataObj;
    lifeDataObj["nominalLifeSeconds"] = toolInfo.lifeData.nominalLifeSeconds;
    lifeDataObj["usedLifeSeconds"] = toolInfo.lifeData.usedLifeSeconds;
    lifeDataObj["warningLifeSeconds"] = toolInfo.lifeData.warningLifeSeconds;
    lifeDataObj["nominalUsageCount"] = toolInfo.lifeData.nominalUsageCount;
    lifeDataObj["currentUsageCount"] = toolInfo.lifeData.currentUsageCount;
    lifeDataObj["warningUsageCount"] = toolInfo.lifeData.warningUsageCount;
    lifeDataObj["currentWearValue"] = toolInfo.lifeData.currentWearValue;
    lifeDataObj["maxWearValue"] = toolInfo.lifeData.maxWearValue;
    lifeDataObj["warningWearValue"] = toolInfo.lifeData.warningWearValue;
    jsonObj["lifeData"] = lifeDataObj;

    // 主轴控制与冷却液设置
    jsonObj["spindleDirection"] = static_cast<int>(toolInfo.spindleDirection);
    jsonObj["coolant1Enabled"] = toolInfo.coolant1Enabled;
    jsonObj["coolant2Enabled"] = toolInfo.coolant2Enabled;

    return jsonObj;
}

bool GoogolToolManager::validateToolInfo(const ToolInfo& toolInfo) {
    // 检查必要字段
    if (toolInfo.uuid.empty()) {
        return false;
    }

    if (toolInfo.name.empty()) {
        return false;
    }

    if (toolInfo.number <= 0) {
        return false;
    }

    if (toolInfo.dNumber <= 0 || toolInfo.dNumber > 9) {
        return false;
    }

    // 检查几何参数的合理性
    if (toolInfo.geometryRadius < 0 || toolInfo.geometryLengthZ < 0) {
        return false;
    }

    // 检查刀具类型代码
    if (toolInfo.toolTypeCode < 0) {
        return false;
    }

    return true;
}

void GoogolToolManager::updateToolLifeData(const std::string& uuid, double usageSeconds, int usageCount) {
    std::lock_guard<std::mutex> lock(m_toolMutex);

    auto it = m_toolParametersByUuid.find(uuid);
    if (it == m_toolParametersByUuid.end()) {
        return;
    }

    ToolInfo& toolInfo = it->second;

    // 更新使用时间
    if (usageSeconds > 0) {
        toolInfo.lifeData.usedLifeSeconds += usageSeconds;
        toolInfo.wasUsed = true;
    }

    // 更新使用次数
    if (usageCount > 0) {
        toolInfo.lifeData.currentUsageCount += usageCount;
        toolInfo.wasUsed = true;
    }

    // 检查是否达到预警阈值
    bool warningReached = false;

    if (toolInfo.lifeData.warningLifeSeconds > 0 &&
        toolInfo.lifeData.usedLifeSeconds >= toolInfo.lifeData.warningLifeSeconds) {
        warningReached = true;
    }

    if (toolInfo.lifeData.warningUsageCount > 0 &&
        toolInfo.lifeData.currentUsageCount >= toolInfo.lifeData.warningUsageCount) {
        warningReached = true;
    }

    if (toolInfo.lifeData.warningWearValue > 0 &&
        toolInfo.lifeData.currentWearValue >= toolInfo.lifeData.warningWearValue) {
        warningReached = true;
    }

    if (warningReached && !toolInfo.isLifeWarningReached) {
        toolInfo.isLifeWarningReached = true;

        // 这里可以触发报警或通知
        // m_interface.setAlarm(...);
    }
}

ErrorCode GoogolToolManager::loadDefaultToolsFromConfigFile() {
    // 注意：此方法假设已经获得了m_toolMutex锁

    std::string filePath = getDefaultToolsFilePath();

    if (!std::filesystem::exists(filePath)) {
        // 如果默认配置文件不存在，创建一些基本的默认刀具
        initializeToolData();
        return ErrorCode::Success;
    }

    std::ifstream file(filePath);
    if (!file.is_open()) {
        // 如果无法打开默认配置文件，创建基本的默认刀具
        initializeToolData();
        return ErrorCode::Success;
    }

    std::string content((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());
    file.close();

    try {
        // 解析 JSON
        json toolData = json::parse(content);

        // 解析刀具参数部分
        if (toolData.contains("tool_parameters")) {
            const json& toolParameters = toolData["tool_parameters"];

            for (const auto& toolJson : toolParameters) {
                try {
                    ToolInfo toolInfo = createToolInfoFromJson(toolJson);

                    // 确保UUID存在
                    if (toolInfo.uuid.empty()) {
                        toolInfo.uuid = generateUuid();
                    }

                    // 默认配置文件中的刀具都是有效的
                    toolInfo.isValid = true;

                    if (validateToolInfo(toolInfo)) {
                        m_toolParametersByUuid[toolInfo.uuid] = toolInfo;
                        updateToolNumberIndex(toolInfo);
                    }
                } catch (const std::exception& e) {
                    // 跳过无效的刀具数据
                    continue;
                }
            }
        }

        return ErrorCode::Success;

    } catch (const json::parse_error& e) {
        // 如果解析失败，创建基本的默认刀具
        initializeToolData();
        return ErrorCode::Success;
    } catch (const std::exception& e) {
        // 如果解析失败，创建基本的默认刀具
        initializeToolData();
        return ErrorCode::Success;
    }
}

// === 主轴和刀库交互方法实现 ===

ErrorCode GoogolToolManager::moveToolFromPocketToSpindle(int channelId, int toolChangerId, int pocketNumber, int spindleIndex) {
    std::lock_guard<std::mutex> lock(m_toolMutex);

    HW_LOG_INFO(m_interface.m_logger, "开始将刀具从刀位移动到主轴: 通道%d 刀库%d 刀位%d 主轴%d",
               channelId, toolChangerId, pocketNumber, spindleIndex);

    if (!m_isInitialized) {
        HW_LOG_ERROR(m_interface.m_logger, "刀具管理器未初始化，无法执行换刀操作");
        return ErrorCode::NotInitialized;
    }

    // 验证参数
    if (channelId < 0 || channelId >= m_interface.m_loadedChannelsCount) {
        HW_LOG_ERROR(m_interface.m_logger, "无效的通道ID: %d", channelId);
        m_interface.m_lastError = "无效的通道ID: " + std::to_string(channelId);
        return ErrorCode::InvalidParam;
    }

    if (toolChangerId < 0 || pocketNumber <= 0) {
        HW_LOG_ERROR(m_interface.m_logger, "无效的刀库ID或刀位号: 刀库%d 刀位%d", toolChangerId, pocketNumber);
        m_interface.m_lastError = "无效的刀库ID或刀位号";
        return ErrorCode::InvalidParam;
    }

    // 检查刀位是否有刀具
    ToolInfo toolInPocket;
    ErrorCode result = getToolInfoInPocket(toolChangerId, pocketNumber, toolInPocket);
    if (result != ErrorCode::Success) {
        HW_LOG_ERROR(m_interface.m_logger, "刀位%d中没有刀具", pocketNumber);
        m_interface.m_lastError = "刀位 " + std::to_string(pocketNumber) + " 中没有刀具";
        return ErrorCode::InvalidState;
    }

    HW_LOG_DEBUG(m_interface.m_logger, "刀位%d中的刀具: T%d %s",
                pocketNumber, toolInPocket.number, toolInPocket.name.c_str());

    // 检查主轴是否已有刀具
    ToolInfo currentSpindleTool;
    ErrorCode spindleResult = getCurrentToolInfo(channelId, currentSpindleTool);
    if (spindleResult == ErrorCode::Success) {
        HW_LOG_ERROR(m_interface.m_logger, "主轴已有刀具T%d，请先卸载", currentSpindleTool.number);
        m_interface.m_lastError = "主轴已有刀具，请先卸载";
        return ErrorCode::InvalidState;
    }

    // 执行刀具交换操作
    HW_LOG_DEBUG(m_interface.m_logger, "开始执行刀具交换操作");

    // 1. 更新主轴状态 - 设置当前刀具号
    if (!m_interface.isInitialized()) {
        HW_LOG_ERROR(m_interface.m_logger, "CNC接口未初始化，无法设置主轴刀具号");
        m_interface.m_lastError = "CNC接口未初始化";
        return ErrorCode::NotInitialized;
    }

    // 设置主轴当前刀具号
    m_interface.m_ncChannelOutPtrArr[channelId].m_shm32Ptr->NC_Status_Mode_Type_T = toolInPocket.number;
    HW_LOG_DEBUG(m_interface.m_logger, "设置主轴刀具号: T%d", toolInPocket.number);

    // 2. 找到刀具对应的H号并设置
    int hNumber = findHNumberForTool(toolInPocket.number, toolInPocket.dNumber);
    if (hNumber > 0) {
        m_interface.m_ncChannelOutPtrArr[channelId].m_shm32Ptr->NC_Status_Mode_Type_H = hNumber;
        HW_LOG_DEBUG(m_interface.m_logger, "设置H模态号: H%d", hNumber);

        // 同步刀补数据到SDK
        ErrorCode syncResult = setToolToDeviceByHIndex(channelId, hNumber - 1, toolInPocket);
        if (syncResult != ErrorCode::Success) {
            HW_LOG_WARN(m_interface.m_logger, "同步刀补数据到SDK失败，但换刀操作继续");
            // 同步失败不影响换刀成功，但记录错误
        } else {
            HW_LOG_DEBUG(m_interface.m_logger, "刀补数据已同步到SDK");
        }
    } else {
        HW_LOG_WARN(m_interface.m_logger, "未找到刀具T%d对应的H号", toolInPocket.number);
    }

    // 3. 更新刀具状态（车床刀具简化：主轴加载后不从刀库移除）
    auto it = m_toolParametersByUuid.find(toolInPocket.uuid);
    if (it != m_toolParametersByUuid.end()) {
        it->second.isActive = true;
        HW_LOG_DEBUG(m_interface.m_logger, "刀具T%d状态已设置为激活，但保留在刀库中", toolInPocket.number);
        // 车床刀具保持在刀库中，不移除
        // it->second.isInFixedLocation = false; // 保持原状态
        // it->second.pocket = 0; // 保持在原刀位
    }

    // 4. 车床刀具简化：不清空刀位，刀具仍保留在刀库中
    HW_LOG_DEBUG(m_interface.m_logger, "车床模式：刀具T%d保留在刀位%d中", toolInPocket.number, pocketNumber);
    // auto& magazine = m_systemMagazines[toolChangerId];
    // if (pocketNumber <= static_cast<int>(magazine.pockets.size())) {
    //     magazine.pockets[pocketNumber - 1].toolUuid = "";
    //     magazine.pockets[pocketNumber - 1].isEmpty = true;
    // }

    // 5. 自动保存配置
    ErrorCode saveResult = saveToolDataToConfigNoLock();
    if (saveResult != ErrorCode::Success) {
        HW_LOG_WARN(m_interface.m_logger, "换刀后保存配置失败，但换刀操作成功");
        // 保存失败不影响换刀成功
    }

    HW_LOG_INFO(m_interface.m_logger, "刀具从刀位移动到主轴完成: T%d %s -> 通道%d主轴",
               toolInPocket.number, toolInPocket.name.c_str(), channelId);
    return ErrorCode::Success;
}

ErrorCode GoogolToolManager::moveToolFromSpindleToPocket(int channelId, int spindleIndex, int toolChangerId, int pocketNumber, int* movedToPocketToolNumber) {
    std::lock_guard<std::mutex> lock(m_toolMutex);

    if (!m_isInitialized) {
        return ErrorCode::NotInitialized;
    }

    // 初始化输出参数
    if (movedToPocketToolNumber) {
        *movedToPocketToolNumber = 0;
    }

    // 验证参数
    if (channelId < 0 || channelId >= m_interface.m_loadedChannelsCount) {
        m_interface.m_lastError = "无效的通道ID: " + std::to_string(channelId);
        return ErrorCode::InvalidParam;
    }

    if (toolChangerId < 0 || pocketNumber <= 0) {
        m_interface.m_lastError = "无效的刀库ID或刀位号";
        return ErrorCode::InvalidParam;
    }

    // 获取当前主轴刀具
    ToolInfo currentSpindleTool;
    ErrorCode result = getCurrentToolInfo(channelId, currentSpindleTool);
    if (result != ErrorCode::Success) {
        m_interface.m_lastError = "主轴中没有刀具";
        return ErrorCode::InvalidState;
    }

    // 检查目标刀位状态（车床简化逻辑：允许刀具回到原刀位）
    ToolInfo toolInPocket;
    ErrorCode pocketResult = getToolInfoInPocket(toolChangerId, pocketNumber, toolInPocket);
    if (pocketResult == ErrorCode::Success) {
        // 车床简化：如果目标刀位的刀具就是当前主轴刀具，则允许"回到"原位
        if (toolInPocket.uuid != currentSpindleTool.uuid) {
            m_interface.m_lastError = "目标刀位 " + std::to_string(pocketNumber) + " 已有其他刀具";
            return ErrorCode::InvalidState;
        }
        // 如果是同一个刀具，继续执行（相当于只是取消主轴激活状态）
    }

    // 执行刀具移动操作
    // 1. 清空主轴状态
    if (!m_interface.isInitialized()) {
        m_interface.m_lastError = "CNC接口未初始化";
        return ErrorCode::NotInitialized;
    }

    // 2. 更新刀具状态
    auto it = m_toolParametersByUuid.find(currentSpindleTool.uuid);
    if (it != m_toolParametersByUuid.end()) {
        // it->second.isActive = false;
        it->second.isInFixedLocation = true; // 在固定位置（刀库）
        it->second.pocket = pocketNumber;
        it->second.toolChangerId = toolChangerId;
    }

    // 3. 更新刀位状态（车床简化：如果刀具本来就在刀位中，则无需更新刀位）
    auto& magazine = m_systemMagazines[toolChangerId];
    if (pocketNumber <= static_cast<int>(magazine.pockets.size())) {
        // 车床简化：只有当刀位为空或刀具不同时才更新刀位状态
        if (magazine.pockets[pocketNumber - 1].isEmpty ||
            magazine.pockets[pocketNumber - 1].toolUuid != currentSpindleTool.uuid) {
            magazine.pockets[pocketNumber - 1].toolUuid = currentSpindleTool.uuid;
            magazine.pockets[pocketNumber - 1].isEmpty = false;
        }
    }

    // 4. 设置输出参数
    if (movedToPocketToolNumber) {
        *movedToPocketToolNumber = currentSpindleTool.number;
    }

    // 5. 自动保存配置
    ErrorCode saveResult = saveToolDataToConfigNoLock();
    if (saveResult != ErrorCode::Success) {
        // 保存失败不影响换刀成功
    }

    return ErrorCode::Success;
}

ErrorCode GoogolToolManager::getMagazineStatus(int toolChangerId, std::vector<PocketStatus>& pocketStatuses) {
    std::lock_guard<std::mutex> lock(m_toolMutex);

    if (!m_isInitialized) {
        return ErrorCode::NotInitialized;
    }

    pocketStatuses.clear();

    // 验证刀库ID
    if (toolChangerId < 0) {
        m_interface.m_lastError = "无效的刀库ID: " + std::to_string(toolChangerId);
        return ErrorCode::InvalidParam;
    }

    // 查找刀库
    auto magazineIt = m_systemMagazines.find(toolChangerId);
    if (magazineIt == m_systemMagazines.end()) {
        m_interface.m_lastError = "刀库 " + std::to_string(toolChangerId) + " 不存在";
        return ErrorCode::InvalidParam;
    }

    const auto& magazine = magazineIt->second;

    // 构建刀位状态列表
    for (size_t i = 0; i < magazine.pockets.size(); ++i) {
        PocketStatus status;
        status.pocketNumber = static_cast<int>(i + 1);
        status.isOccupied = !magazine.pockets[i].isEmpty;
        status.toolNumberInPocket = 0;

        // 设置刀位状态标志
        status.isEnabled = true; // 默认启用，可以根据需要从配置中读取
        status.isWorkingPosition = (i == 0); // 第一个刀位作为工作位，可以根据需要调整

        // 如果刀位不为空，获取刀具信息
        if (status.isOccupied && !magazine.pockets[i].toolUuid.empty()) {
            auto toolIt = m_toolParametersByUuid.find(magazine.pockets[i].toolUuid);
            if (toolIt != m_toolParametersByUuid.end()) {
                const ToolInfo& toolInfo = toolIt->second;
                status.toolNumberInPocket = toolInfo.number;
            } else {
                // 刀具UUID存在但找不到刀具信息，标记为空
                status.isOccupied = false;
                status.toolNumberInPocket = 0;
            }
        }

        pocketStatuses.push_back(status);
    }

    return ErrorCode::Success;
}

ErrorCode GoogolToolManager::getToolChangersConfigs(std::vector<ToolChangerConfig>& toolChangersConfigs) {
    std::lock_guard<std::mutex> lock(m_toolMutex);

    if (!m_isInitialized) {
        return ErrorCode::NotInitialized;
    }

    toolChangersConfigs.clear();

    // 根据 m_systemMagazines 构建刀库配置
    for (const auto& [magazineId, magazineInfo] : m_systemMagazines) {
        ToolChangerConfig config;
        config.name = magazineInfo.name;
        config.capacity = magazineInfo.capacity;

        // 根据刀库ID和名称推断刀库类型
        if (magazineInfo.name.find("主刀库") != std::string::npos ||
            magazineInfo.name.find("主库") != std::string::npos) {
            config.type = ToolChangerType::CAROUSEL;  // 主刀库通常是盘式
            config.supportsRandomSelection = true;
            config.averageChangeTime = std::chrono::duration<double>(5.0);  // 5秒换刀时间
        } else if (magazineInfo.name.find("副刀库") != std::string::npos ||
                   magazineInfo.name.find("副库") != std::string::npos) {
            config.type = ToolChangerType::CHAIN;     // 副刀库通常是链式
            config.supportsRandomSelection = false;
            config.averageChangeTime = std::chrono::duration<double>(8.0);  // 8秒换刀时间
        } else {
            config.type = ToolChangerType::CAROUSEL;  // 默认为盘式
            config.supportsRandomSelection = true;
            config.averageChangeTime = std::chrono::duration<double>(6.0);  // 6秒换刀时间
        }

        // 设置刀具限制参数
        config.maxToolWeightKg = 10.0;      // 最大10kg刀具
        config.maxToolDiameterMm = 100.0;   // 最大直径100mm
        config.maxToolLengthMm = 300.0;     // 最大长度300mm

        toolChangersConfigs.push_back(config);
    }

    // 如果没有配置的刀库，创建一个默认的
    if (toolChangersConfigs.empty()) {
        ToolChangerConfig defaultConfig;
        defaultConfig.name = "主刀库";
        defaultConfig.type = ToolChangerType::CAROUSEL;
        defaultConfig.capacity = 24;
        defaultConfig.supportsRandomSelection = true;
        defaultConfig.averageChangeTime = std::chrono::duration<double>(5.0);
        defaultConfig.maxToolWeightKg = 10.0;
        defaultConfig.maxToolDiameterMm = 100.0;
        defaultConfig.maxToolLengthMm = 300.0;
        toolChangersConfigs.push_back(defaultConfig);
    }

    return ErrorCode::Success;
}

ErrorCode GoogolToolManager::loadMagazineConfigFromFile() {
    // 注意：此方法假设已经获得了m_toolMutex锁

    // 尝试从配置文件加载刀库配置
    std::filesystem::path configPath = std::filesystem::path(m_interface.m_writablePath) / "TOOLS" / "MagazineConfig.json";
    if (!std::filesystem::exists(configPath)) {
        // 配置文件不存在，使用默认配置
        return ErrorCode::FileNotFound;
    }

    std::ifstream file(configPath);
    if (!file.is_open()) {
        return ErrorCode::FileError;
    }

    std::string content((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());
    file.close();

    try {
        json magazineConfig = json::parse(content);

        // 清空现有刀库数据
        m_systemMagazines.clear();

        if (magazineConfig.contains("magazines")) {
            const json& magazines = magazineConfig["magazines"];

            for (const auto& magazineJson : magazines) {
                GoogolMagazineInfo magazine;
                magazine.id = magazineJson.value("id", 0);
                magazine.name = magazineJson.value("name", "默认刀库");
                magazine.capacity = magazineJson.value("capacity", 24);

                // 初始化刀位
                magazine.pockets.resize(magazine.capacity);
                for (int i = 0; i < magazine.capacity; ++i) {
                    magazine.pockets[i].isEmpty = true;
                    magazine.pockets[i].toolUuid = "";
                }

                m_systemMagazines[magazine.id] = magazine;
            }
        }

        return ErrorCode::Success;

    } catch (const json::parse_error& e) {
        return ErrorCode::FileError;
    } catch (const std::exception& e) {
        return ErrorCode::FileError;
    }
}

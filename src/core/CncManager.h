/**
 * @file CncManager.h
 * @brief CNC管理器，负责与ICncInterface交互的核心管理类
 *
 * 该管理器封装了CNC接口的基本功能调用，维护系统状态，
 * 并提供事件监听和分发机制。
 */
#pragma once

#include <QMutex>
#include <QObject>
#include <QThread>
#include <memory>

#include "public/ICncEventSystem.h"
#include "public/ICncInterface.h"

/**
 * @brief CNC管理器主类
 *
 * 负责与CNC控制器的所有交互，包括：
 * - 接口生命周期管理
 * - 状态监控和缓存
 * - 事件接收和分发
 * - 基本功能调用封装
 */
class CncManager : public QObject, public ICncEventListener {
    Q_OBJECT

   public:
    /**
     * @brief 获取CNC管理器单例实例
     * @return CncManager单例指针
     */
    static CncManager* getInstance();

    /**
     * @brief 析构函数
     */
    ~CncManager();

    // --- 生命周期管理 ---
    /**
     * @brief 初始化CNC接口
     * @param configPath 配置文件路径
     * @return 操作结果
     */
    ErrorCode initialize(const std::string& configPath = "", const std::string& writablePath = "");

    /**
     * @brief 关闭CNC接口
     * @return 操作结果
     */
    ErrorCode shutdown();

    /**
     * @brief 检查是否已初始化
     * @return true表示已初始化
     */
    bool isInitialized() const;

    // --- 状态获取 ---
    /**
     * @brief 获取系统配置（缓存版本）
     * @return 系统配置引用
     */
    const SystemConfig& getSystemConfig() const;

    /**
     * @brief 获取系统状态（缓存版本）
     * @return 当前系统状态
     */
    CncSystemState getSystemState() const;

    /**
     * @brief 获取指定通道的操作模式（缓存版本）
     * @param channelId 通道ID
     * @return 操作模式
     */
    OperatingMode getOperatingMode(int channelId) const;

    /**
     * @brief 设置指定通道的操作模式
     * @param channelId 通道ID
     * @param mode 操作模式
     * @return 操作结果
     */
    ErrorCode setOperatingMode(int channelId, OperatingMode mode);

    /**
     * @brief 读取错误
     * @param errorMsg
     */
    ErrorCode getLastError(std::string& errorMsg);

    /**
     * @brief 获取指定通道的程序状态（缓存版本）
     * @param channelId 通道ID
     * @return 程序状态引用
     */
    const ProgramStatus& getProgramStatus(int channelId) const;

    /**
     * @brief 获取指定通道当前活动的G代码功能列表
     * @param channelId 通道ID
     * @param functions 输出G代码功能列表
     * @return 操作结果
     */
    ErrorCode getActiveGFunctionList(int channelId, std::vector<ActiveGFunction>& functions);

    // --- 坐标与位置 ---
    /**
     * @brief 获取指定通道的工件坐标
     * @param channelId 通道ID
     * @param position 输出坐标
     * @param isReadFromCNC true从CNC读取，false自己计算
     * @return 操作结果
     */
    ErrorCode getWorkPosition(int channelId, PointXD& position, bool isReadFromCNC = false);

    /**
     * @brief 获取指定通道的机床坐标
     * @param channelId 通道ID
     * @param position 输出坐标
     * @return 操作结果
     */
    ErrorCode getMachinePosition(int channelId, PointXD& position);

    // --- 进给与主轴 ---
    /**
     * @brief 获取指定通道的实际进给速度
     * @param channelId 通道ID
     * @param feedrate 输出进给速度
     * @return 操作结果
     */
    ErrorCode getActualFeedrate(int channelId, double& feedrate);

    /**
     * @brief 设置进给倍率
     * @param channelId 通道ID
     * @param overridePercent 倍率百分比
     * @return 操作结果
     */
    ErrorCode setFeedOverride(int channelId, double overridePercent);

    /**
     * @brief 获取主轴转速
     * @param channelId 通道ID
     * @param spindleIndex 主轴索引
     * @param speedRpm 输出转速
     * @return 操作结果
     */
    ErrorCode getActualSpindleSpeed(int channelId, int spindleIndex, double& speedRpm);

    /**
     * @brief 获取指令进给速度
     * @param channelId 通道ID
     * @param feedrate 输出指令进给速度
     * @return 操作结果
     */
    ErrorCode getCommandFeedrate(int channelId, double& feedrate);

    /**
     * @brief 获取进给倍率
     * @param channelId 通道ID
     * @param overridePercent 输出倍率百分比
     * @return 操作结果
     */
    ErrorCode getFeedOverride(int channelId, double& overridePercent);

    /**
     * @brief 获取指令主轴转速
     * @param channelId 通道ID
     * @param spindleIndex 主轴索引
     * @param speedRpm 输出指令转速
     * @return 操作结果
     */
    ErrorCode getCommandSpindleSpeed(int channelId, int spindleIndex, double& speedRpm);

    /**
     * @brief 获取主轴倍率
     * @param channelId 通道ID
     * @param overridePercent 输出倍率百分比
     * @return 操作结果
     */
    ErrorCode getSpindleOverride(int channelId, double& overridePercent);

    /**
     * @brief 检查主轴是否运行
     * @param channelId 通道ID
     * @param spindleIndex 主轴索引
     * @param isOn 输出主轴状态
     * @return 操作结果
     */
    ErrorCode isSpindleOn(int channelId, int spindleIndex, bool& isOn);

    // --- 程序执行 ---
    /**
     * @brief 加载程序文件
     * @param channelId 通道ID
     * @param filePath 文件路径
     * @return 操作结果
     */
    ErrorCode loadProgram(int channelId, const std::string& filePath);

    /**
     * @brief 编译程序文件
     * @param channelId 通道ID
     * @param filePath 文件路径
     * @param[out] gMotions 用于接收编译后的运动指令的向量。
     * @return 操作结果
     */
    ErrorCode compileProgram(int channelId, const std::string filePath, std::vector<GMotion>& gMotions);

    /**
     * @brief 启动程序执行
     * @param channelId 通道ID
     * @return 操作结果
     */
    ErrorCode startProgram(int channelId);

    /**
     * @brief 暂停程序执行
     * @param channelId 通道ID
     * @return 操作结果
     */
    ErrorCode pauseProgram(int channelId);

    /**
     * @brief 恢复程序执行
     * @param channelId 通道ID
     * @return 操作结果
     */
    ErrorCode resumeProgram(int channelId);

    /**
     * @brief 停止程序执行
     * @param channelId 通道ID
     * @return 操作结果
     */
    ErrorCode stopProgram(int channelId);

    /**
     * @brief 复位程序
     * @param channelId 通道ID
     * @return 操作结果
     */
    ErrorCode resetProgram(int channelId);

    // --- 手动操作 ---
    /**
     * @brief 开始轴点动
     * @param channelId 通道ID
     * @param axisIndex 轴索引
     * @param speed 点动速度
     * @param distance 点动距离
     * @return 操作结果
     */
    ErrorCode startJog(int channelId, int axisIndex, double speed, double distance = 0.0);

    /**
     * @brief 停止轴点动
     * @param channelId 通道ID
     * @param axisIndex 轴索引
     * @return 操作结果
     */
    ErrorCode stopJog(int channelId, int axisIndex);

    // --- MDI操作 ---
    /**
     * @brief 执行MDI指令
     * @param channelId 通道ID
     * @param command MDI指令
     * @return 操作结果
     */
    ErrorCode executeMdi(int channelId, const std::string& command);

    // --- 宏操作 ---
    /**
     * @brief 执行宏指令
     * @param channelId 通道ID
     * @param command 宏指令
     * @return 操作结果
     */
    ErrorCode executeMacro(int channelId, const std::string& macro);

    /**
     * @brief 获取指定通道下特定轴的总零偏。
     * @param channelId 目标通道的ID。
     * @param axisIndex 轴名称。
     * @param[out] totalOffset 用于接收该轴总零偏。
     * @return ErrorCode 指示操作成功或失败。
    */
    ErrorCode getAxisTotalOffset(int channelId, int axisIndex, double& totalOffset);

    // --- 坐标系管理 ---
    /**
     * @brief 获取当前工件坐标系名称
     * @param channelId 通道ID
     * @param workOffsetName 输出坐标系名称
     * @return 操作结果
     */
    ErrorCode getCurrentWorkOffsetName(int channelId, std::string& workOffsetName);

    /**
     * @brief 获取指定通道特定工件坐标系的偏移值
     * @param channelId 通道ID
     * @param workOffsetName 坐标系名称
     * @param offset 输出偏移值
     * @return 操作结果
     */
    ErrorCode getWorkOffsetValue(int channelId, const std::string& workOffsetName, PointXD& offset);

    /**
     * @brief 设置指定通道的工件坐标系偏移值
     * @param channelId 通道ID
     * @param workOffsetName 坐标系名称
     * @param offset 偏移值
     * @return 操作结果
     */
    ErrorCode setWorkOffsetValue(int channelId, const std::string& workOffsetName, PointXD& offset);

    /**
     * @brief 获取剩余移动距离
     * @param channelId 通道ID
     * @param remainingPos 输出剩余距离
     * @return 操作结果
     */
    ErrorCode getRemainingPosition(int channelId, PointXD& remainingPos);

    // --- 程序控制选项 ---
    /**
     * @brief 设置可选停止
     * @param channelId 通道ID
     * @param enabled 是否启用
     * @return 操作结果
     */
    ErrorCode setOptionalStop(int channelId, bool enabled);

    /**
     * @brief 设置手轮模式
     * @param channelId 通道ID
     * @param enabled 是否启用
     * @return 操作结果
     */
    ErrorCode setHandwheelMode(int channelId, bool enabled);

    /**
     * @brief 设置程序段跳过
     * @param channelId 通道ID
     * @param enabled 是否启用
     * @return 操作结果
     */
    ErrorCode setBlockSkip(int channelId, bool enabled);

    /**
     * @brief 设置单步模式
     * @param channelId 通道ID
     * @param mode 单步模式类型
     * @return 操作结果
     */
    ErrorCode setSingleStepMode(int channelId, SingleBlockModeType mode);

    // --- 宏变量管理 ---
    /**
     * @brief 读取指定通道的宏变量值 (#索引)
     * @param channelId 目标通道的ID
     * @param index 要读取的宏变量的索引号
     * @param value 用于接收宏变量值的 double 型引用
     * @return 操作结果
     */
    ErrorCode getMacroVariable(int channelId, int index, double& value);

    /**
     * @brief 写入指定通道的宏变量值 (#索引)
     * @param channelId 目标通道的ID
     * @param index 要写入的宏变量的索引号
     * @param value 要写入的值
     * @return 操作结果
     */
    ErrorCode setMacroVariable(int channelId, int index, double value);

    // --- 刀具管理 ---
    /**
     * @brief 获取当前刀具信息
     * @param channelId 通道ID
     * @param toolInfo 输出刀具信息
     * @return 操作结果
     */
    ErrorCode getCurrentToolInfo(int channelId, ToolInfo& toolInfo);

    // === 新的基于 UUID 的刀具管理接口 ===
    /**
     * @brief 获取刀具参数（基于UUID）
     * @param uuid 刀具UUID
     * @param toolInfo 输出刀具信息
     * @return 操作结果
     */
    ErrorCode getToolParameters(const std::string& uuid, ToolInfo& toolInfo);

    /**
     * @brief 设置刀具参数（基于UUID）
     * @param toolInfo 刀具信息（包含UUID）
     * @return 操作结果
     */
    ErrorCode setToolParameters(const ToolInfo& toolInfo);

    /**
     * @brief 删除刀具（基于UUID）
     * @param uuid 刀具UUID
     * @return 操作结果
     */
    ErrorCode deleteTool(const std::string& uuid);

    /**
     * @brief 获取所有刀具参数（基于UUID）
     * @param allToolsInfo 用于接收所有刀具信息的map，key为UUID，value为刀具信息
     * @return 操作结果
     */
    ErrorCode getAllToolParameters(std::map<std::string, ToolInfo>& allToolsInfo);

    /**
     * @brief 获取刀库状态
     * @param toolChangerId 刀库ID
     * @param pocketStatuses 输出刀位状态列表
     * @return 操作结果
     */
    ErrorCode getMagazineStatus(int toolChangerId, std::vector<PocketStatus>& pocketStatuses);

    // --- 配置管理 ---
    /**
     * @brief 获取系统配置层次结构
     * @param rootCategories 输出配置分类树
     * @return 操作结果
     */
    ErrorCode getConfiguration(std::vector<ConfigCategory>& rootCategories);

    /**
     * @brief 设置系统配置层次结构
     * @param rootCategories 配置分类树
     * @return 操作结果
     */
    ErrorCode setConfiguration(std::vector<ConfigCategory>& rootCategories);

    // --- 系统信息打印 ---
    /**
     * @brief 打印系统信息到控制台
     *
     * 输出系统的详细配置信息，包括：
     * - 系统版本和控制器信息
     * - 通道配置信息
     * - 轴配置信息
     * - 主轴配置信息
     * - 刀库配置信息
     * - 系统性能参数
     * - 当前系统状态
     */
    void printSystemInfo() const;

    /**
     * @brief 打印系统状态信息到控制台
     *
     * 输出当前系统的运行状态信息，包括：
     * - 系统总体状态
     * - 各通道操作模式和程序状态
     * - 当前活动报警信息
     * - 坐标和运动状态
     */
    void printSystemStatus() const;

    /**
     * @brief 打印详细的轴信息到控制台
     * @param channelId 通道ID，-1表示打印所有通道的轴信息
     *
     * 输出指定通道或所有通道的轴详细信息，包括：
     * - 轴配置参数
     * - 当前位置（工件坐标、机床坐标）
     * - 运动状态和限位状态
     */
    void printAxisInfo(int channelId = -1) const;

    /**
     * @brief 打印刀具管理信息到控制台
     *
     * 输出刀具管理的详细信息，包括：
     * - 刀库配置和状态
     * - 刀具参数信息
     * - 当前活动刀具信息
     */
    void printToolInfo() const;

    // --- 报警处理 ---
    /**
     * @brief 获取活动报警列表（缓存版本）
     * @return 报警列表引用
     */
    const std::vector<AlarmInfo>& getActiveAlarms() const;

    /**
     * @brief 清除报警
     * @param channelId 通道ID，-1表示所有通道
     * @return 操作结果
     */
    ErrorCode clearAlarms(int channelId = -1);

    // --- 事件处理 ---
    // --- ICncEventListener接口实现 ---
    /**
     * @brief 接收CNC事件通知（ICncEventListener接口）
     * @param event CNC事件
     */
    virtual void onCncEvent(const CncEvent& event) override;

    // --- 事件创建和测试方法 ---
    /**
     * @brief 模拟面板按键事件（用于测试）
     * @param keyType 按键类型
     * @param action 按键动作
     * @param channelId 通道ID
     * @param value 按键值
     */
    void simulatePanelKeyEvent(PanelKeyType keyType, PanelKeyAction action, int channelId = -1, double value = 0.0);

    /**
     * @brief 模拟系统状态变化事件（用于测试）
     * @param newState 新状态
     */
    void simulateSystemStateChange(CncSystemState newState);

    /**
     * @brief 模拟报警事件（用于测试）
     * @param alarm 报警信息
     * @param isOccurred true表示报警发生，false表示报警清除
     */
    void simulateAlarmEvent(const AlarmInfo& alarm, bool isOccurred = true);

    /**
     * @brief 检查是否有任何通道的程序正在运行
     * @return true表示有程序正在运行
     */
    bool isAnyProgramRunning() const;

    // --- 螺距补偿 ---
    /**
     * @brief 获取所有螺距补偿
     * @param compensations 输出螺距补偿列表
     * @return 操作结果
     */
    ErrorCode getPitchCompensation(int channelId, int axisIndex, AxisPitchCompensation& compensation);

    /**
     * @brief 设置螺距补偿
     * @param channelId 通道ID
     * @param axisIndex 轴索引
     * @param compensation 螺距补偿
     * @return 操作结果
     */
    ErrorCode setPitchCompensation(int channelId, int axisIndex, const AxisPitchCompensation& compensation);

    /**
     * @brief 启用螺距补偿
     * @param channelId 通道ID
     * @param axisIndex 轴索引
     * @param enable 是否启用
     * @param isCyclicMode 是否循环模式
     * @return 操作结果
     */
    ErrorCode enablePitchCompensation(int channelId, int axisIndex, bool enable, bool isCyclicMode = false);

    /**
     * @brief 获取螺距补偿状态
     * @param channelId 通道ID
     * @param axisIndex 轴索引
     * @param isEnabled 是否启用
     * @param isCyclicMode 是否循环模式
     * @param currentCompensation 当前补偿值
     * @return 操作结果
     */
    ErrorCode getPitchCompensationStatus(int channelId, int axisIndex, bool& isEnabled, bool& isCyclicMode,
                                         double& currentCompensation);

    /**
     * @brief 获取所有螺距补偿
     * @param channelId 通道ID
     * @param allCompensations 输出所有螺距补偿列表
     * @return 操作结果
     */
    ErrorCode getAllPitchCompensations(int channelId, std::vector<AxisPitchCompensation>& allCompensations);

    /**
     * @brief 设置所有螺距补偿
     * @param channelId 通道ID
     * @param compensations 螺距补偿列表
     * @return 操作结果
     */
    ErrorCode setAllPitchCompensations(int channelId, const std::vector<AxisPitchCompensation>& compensations);

    // --- UI模态模式通知 ---
    /**
     * @brief 通知hardware层UI已进入模态模式
     * @param modalType 模态模式类型
     * @param modalId 可选的模态实例标识符
     * @return 操作结果
     */
    ErrorCode enterModalMode(UiModalType modalType, const std::string& modalId = "");

    /**
     * @brief 通知hardware层UI已退出模态模式
     * @param modalType 模态模式类型
     * @param modalId 可选的模态实例标识符
     * @return 操作结果
     */
    ErrorCode exitModalMode(UiModalType modalType, const std::string& modalId = "");

   signals:
    // --- 状态变化信号 ---
    /**
     * @brief 系统状态变化信号
     * @param newState 新状态
     */
    void systemStateChanged(CncSystemState newState);

    /**
     * @brief 操作模式变化信号
     * @param channelId 通道ID
     * @param newMode 新模式
     */
    void operatingModeChanged(int channelId, OperatingMode newMode);

    /**
     * @brief 程序状态变化信号
     * @param channelId 通道ID
     * @param status 程序状态
     */
    void programStatusChanged(int channelId, const ProgramStatus& status);

    /**
     * @brief 坐标位置变化信号
     * @param channelId 通道ID
     * @param workPos 工件坐标
     * @param machinePos 机床坐标
     */
    void positionChanged(int channelId, const PointXD& workPos, const PointXD& machinePos);

    /**
     * @brief 进给速度变化信号
     * @param channelId 通道ID
     * @param feedrate 进给速度
     */
    void feedrateChanged(int channelId, double feedrate);

    /**
     * @brief 主轴转速变化信号
     * @param channelId 通道ID
     * @param spindleIndex 主轴索引
     * @param speedRpm 转速
     */
    void spindleSpeedChanged(int channelId, int spindleIndex, double speedRpm);

    /**
     * @brief 报警状态变化信号
     * @param alarms 当前活动报警列表
     */
    void alarmsChanged(const std::vector<AlarmInfo>& alarms);

    // --- 错误信号 ---
    /**
     * @brief CNC接口错误信号
     * @param errorCode 错误码
     * @param errorMessage 错误消息
     */
    void cncError(ErrorCode errorCode, const QString& errorMessage);

    // --- 面板按键信号 ---
    /**
     * @brief 面板按键事件信号
     * @param keyType 按键类型
     * @param action 按键动作
     * @param channelId 相关通道ID
     * @param value 按键值（用于倍率调节等）
     */
    void panelKeyEvent(PanelKeyType keyType, PanelKeyAction action, int channelId, double value);

    /**
     * @brief 刀具变化信号
     * @param channelId 通道ID
     * @param toolInfo 刀具信息
     */
    void toolChanged(int channelId, const ToolInfo& toolInfo);

    /**
     * @brief 工件坐标系变化信号
     * @param channelId 通道ID
     * @param workOffsetName 坐标系名称
     * @param offsetValue 偏置值
     */
    void workOffsetChanged(int channelId, const QString& workOffsetName, const PointXD& offsetValue);

    /**
     * @brief 回零完成信号
     * @param channelId 通道ID
     * @param axisIndices 轴索引列表
     * @param success 是否成功
     * @param message 消息
     */
    void homingCompleted(int channelId, const std::vector<int>& axisIndices, bool success, const QString& message);

    // 新增的私有信号，用于跨线程传递事件
    void eventReceived(const CncEvent& event);

   private slots:
    void processQueuedCncEvent(const CncEvent& event);

   private:
    /**
     * @brief 私有构造函数（单例模式）
     */
    CncManager();

    /**
     * @brief 禁用拷贝构造函数
     */
    CncManager(const CncManager&) = delete;

    /**
     * @brief 禁用赋值操作符
     */
    CncManager& operator=(const CncManager&) = delete;

    /**
     * @brief 更新系统配置缓存
     */
    void updateSystemConfig();

    /**
     * @brief 发射错误信号
     * @param errorCode 错误码
     * @param context 错误上下文
     */
    void emitError(ErrorCode errorCode, const QString& context);

    /**
     * @brief 检查指定按键是否为MCP模式切换按键
     * @param keyType 按键类型
     * @return true表示是模式切换按键
     */
    bool isMcpModeSwitchKey(PanelKeyType keyType) const;

    /**
     * @brief 检查面板按键是否应该被阻止（程序执行时阻止模式切换）
     * @param keyType 按键类型
     * @param action 按键动作
     * @return true表示应该阻止此按键事件
     */
    bool shouldBlockPanelKey(PanelKeyType keyType, PanelKeyAction action) const;

    /**
     * @brief 打印指定通道的轴信息（私有辅助方法）
     * @param channelId 通道ID
     */
    void printAxisInfoForChannel(int channelId) const;

   private:
    static CncManager* s_instance;  ///< 单例实例
    static QMutex s_instanceMutex;  ///< 单例互斥锁

    std::unique_ptr<ICncInterface> m_cncInterface;  ///< CNC接口实例

    mutable QMutex m_dataMutex;  ///< 数据访问互斥锁

    // --- 缓存数据 ---
    SystemConfig m_systemConfig;                     ///< 系统配置缓存
    CncSystemState m_systemState;                    ///< 系统状态缓存
    std::map<int, OperatingMode> m_operatingModes;   ///< 操作模式缓存
    std::map<int, ProgramStatus> m_programStatuses;  ///< 程序状态缓存
    std::vector<AlarmInfo> m_activeAlarms;           ///< 活动报警缓存

    bool m_initialized;  ///< 初始化标志
};

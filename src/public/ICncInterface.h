/**
 * @file ICncInterface.h
 * @brief 定义了与CNC控制器交互的抽象接口和相关数据结构。
 * 这个文件包含了所有必要的枚举、结构体和ICncInterface类，
 * 用于实现对CNC系统的标准化访问。
 */
#pragma once

#include <array>    // 用于 std::array 结构体
#include <chrono>   // 用于现代C++时间类型
#include <cstdint>  // 用于定宽整数
#include <map>      // 用于 PointXD 和 ConfigNode metadata (如果使用)
#include <unordered_map>  // 用于 nameToConfigMap
#include <string>
#include <variant>  // 用于 ConfigValue
#include <vector>

#include "ErrorCode.h"   // 引入通用错误码定义
#include "IAppLogger.h"  // 引入抽象日志接口

// --- 前向声明 (如果复杂类型需要) ---
struct ConfigNode;        // 单个配置项
struct ConfigCategory;    // 配置分类
class ICncEventListener;  // 新增：前向声明 ICncEventListener

// --- CNC接口初始化参数 ---

/**
 * @brief CNC接口初始化参数结构体
 *
 * 包含Hardware实现者在初始化时可能需要的所有参数，
 * 为未来扩展提供了良好的可扩展性。
 */
struct CncInitializationParams {
    // --- 基础路径参数 ---
    std::string configPath;    ///< 配置文件路径（可选，空表示使用默认配置）
    std::string writablePath;  ///< 可写目录路径，用于日志、临时文件、缓存等（可选）

    // --- 日志接口 ---
    IAppLogger* logger = nullptr;  ///< 日志输出接口，nullptr表示不输出日志或使用内部默认日志

    // --- 扩展参数 ---
    /**
     * @brief 自定义扩展参数，键值对形式
     *
     * 用于传递Hardware特定的配置参数，例如：
     * - "device_id": "0"
     * - "baudrate": "115200"
     * - "protocol_version": "2.0"
     * - "custom_dll_path": "/path/to/custom.dll"
     */
    std::map<std::string, std::string> customParameters;

    // --- 便利构造函数 ---
    CncInitializationParams() = default;

    /**
     * @brief 使用配置文件路径构造参数
     */
    explicit CncInitializationParams(const std::string& config) : configPath(config) {}

    /**
     * @brief 使用配置路径、可写路径和日志接口构造参数
     */
    CncInitializationParams(const std::string& config, const std::string& writable, IAppLogger* log = nullptr)
        : configPath(config), writablePath(writable), logger(log) {}
};

// --- 配置值与范围定义 ---

/**
 * @brief 配置项可以持有的值的类型集合。
 * 使用 std::variant允许多种基础数据类型。
 */
using ConfigValue = std::variant<bool, int32_t, int64_t, double, std::string>;

/**
 * @brief 整数范围定义。
 * 用于限制整数配置参数的取值范围和步长。
 */
struct IntRange {
    int64_t min;       ///< 允许的最小值。
    int64_t max;       ///< 允许的最大值。
    int64_t step = 1;  ///< 步长，默认为1。
};

/**
 * @brief 浮点数范围定义。
 * 用于限制浮点数配置参数的取值范围和步长。
 */
struct DoubleRange {
    double min;         ///< 允许的最小值。
    double max;         ///< 允许的最大值。
    double step = 0.0;  ///< 步长，0.0表示连续值，无固定步长。
};

/**
 * @brief 枚举类型的单个选项定义。
 * 用于表示枚举型配置参数的一个可选值及其显示名称。
 */
struct EnumValueOption {
    std::string displayName;  ///< 显示给用户的名称，可能支持本地化。
    ConfigValue actualValue;  ///< 该选项对应的实际配置值 (可以是 int, string 等)。
};

/**
 * @brief 枚举类型的可选值列表。
 * 表示一个枚举配置参数所有可能的选项。
 */
using EnumRange = std::vector<EnumValueOption>;

/**
 * @brief 字符串类型参数的约束定义。
 * 用于限制字符串配置参数的特性，如最大长度和格式。
 */
struct StringRange {
    uint32_t maxLength = 0;    ///< 字符串允许的最大长度，0表示无限制。
    std::string regexPattern;  ///< 用于校验字符串格式的正则表达式 (可选)。
};

/**
 * @brief 配置项的参数范围类型集合。
 * 使用 std::variant 存储不同类型的参数范围或约束。
 */
using ConfigRange = std::variant<std::monostate,  // 表示无特定范围、不适用或类型为bool/string(无特定预设选项)
                                 IntRange, DoubleRange,
                                 EnumRange,  // 用于提供一组固定的选择，无论ConfigValue是string还是int
                                 StringRange  // 主要用于字符串长度等约束，如果字符串有固定选项，也可用EnumRange
                                 >;

/**
 * @brief 配置项值的语义类型。
 */
enum class ConfigSemanticType : uint8_t { Boolean, Integer, Float, String, Enum, Unknown };

/**
 * @brief 配置节点结构体，代表一个具体的配置参数。
 * 包含参数的ID、名称、描述、当前值、默认值、范围、单位和只读状态等信息。
 */
struct ConfigNode {
    std::string id;           ///< 全局唯一标识符 (例如 "axes.X.max_feedrate", "system.display.language")。
    std::string name;         ///< 显示名称 (例如 "X轴最大进给", "显示语言")，可用于UI本地化。
    std::string description;  ///< 详细描述 (可选，用于UI提示等)。
    /**
     * @brief 值的语义类型。
     * range 字段中实际存储的类型应与 semanticType 相匹配。
     */
    ConfigSemanticType semanticType = ConfigSemanticType::Unknown;
    ConfigValue currentValue;  ///< 当前的参数值。
    ConfigValue defaultValue;  ///< 该配置项的系统默认值 (可选，用于恢复默认设置)。
    /**
     * @brief 参数的有效范围、选项或约束。
     * 其具体类型应与semanticType匹配, 例如 IntRange, EnumRange。
     */
    ConfigRange range;
    std::string unit;         ///< 单位 (可选, 例如 "mm/min", "rpm", "%")。
    bool isReadOnly = false;  ///< 该配置项是否为只读。
};

/**
 * @brief 配置分类结构体，用于组织配置节点。
 * 配置项可以按类别进行分组，形成树状结构。
 */
struct ConfigCategory {
    std::string categoryId;                     ///< 分类唯一ID (例如 "axes", "spindle_0")。
    std::string categoryName;                   ///< 分类显示名称 (例如 "轴参数", "主轴设置")。
    std::string categoryDescription;            ///< 分类描述 (可选)。
    std::vector<ConfigNode> configNodes;        ///< 该分类下直接包含的配置项列表。
    std::vector<ConfigCategory> subCategories;  ///< 子分类列表，形成分类的树状结构。
};

// --- 通用枚举和结构体 ---

/**
 * @brief 轴类型枚举。
 * 定义CNC机床中轴的基本类型。
 */
enum class AxisType : uint8_t {
    Linear = 0,  ///< 线性轴 (例如 X, Y, Z)。
    Rotary,      ///< 旋转轴 (例如 A, B, C)。
    Unknown      ///< 未知或未指定类型的轴。
};

/**
 * @brief 距离单位枚举。
 * 定义系统中使用的线性距离单位。
 */
enum class DistanceUnit : uint8_t {
    Millimeter = 0,  ///< 毫米。
    Inch,            ///< 英寸。
    Unknown          ///< 未知或未指定的距离单位。
};

/**
 * @brief 角度单位枚举。
 * 定义系统中使用的角度单位。
 */
enum class AngleUnit : uint8_t {
    Degree = 0,  ///< 度。
    Radian,      ///< 弧度。
    Unknown      ///< 未知或未指定的角度单位。
};

/**
 * @brief 主轴旋转方向枚举。
 * 定义刀具的主轴旋转方向设置。
 */
enum class SpindleDirection : uint8_t {
    Stop = 0,  ///< 主轴停止。
    CW,        ///< 顺时针旋转（M03）。
    CCW        ///< 逆时针旋转（M04）。
};

/**
 * @brief CNC 系统状态枚举。
 * 描述CNC系统的主要宏观运行状态。
 */
enum class CncSystemState : uint8_t {
    Idle = 0,  ///< 空闲状态
    Offline,   ///< 系统离线或未与硬件建立连接。
    Stopped,   ///< 系统已停止，或处于复位/空闲状态，等待指令。
    Running,   ///< 系统正在自动运行G代码程序或MDI指令。
    Paused,    ///< 系统运行已暂停 (进给保持状态)。
    /**
     * @brief 手动模式激活状态。
     * 例如：点动模式、手轮模式或步进模式正在被用户主动操作或已选择等待用户输入。
     */
    ManualActive,
    Homing,      ///< 系统正在执行回零程序。
    Simulating,  ///< 系统正在执行程序仿真 (通常无实际轴运动)。
    Error,       ///< 系统发生错误，无法正常运行。
    Unknown      ///< 未知或未定义的系统状态。
};

/**
 * @brief CNC 操作模式枚举。
 * 定义CNC机床当前的主要操作模式。
 */
enum class OperatingMode : uint8_t {
    Auto = 0,  ///< 自动模式 (执行存储的程序)。
    MDI,       ///< 手动数据输入模式 (执行单条或少量指令)。
    Manual,    ///< 手动模式 (包括点动JOG, 步进STEP, 手轮MPG/Handwheel)。
    Edit,      ///< 编辑模式 (编辑G代码程序)。
    Home,      ///< 回零模式 (专门用于执行回零操作的模式)。
    Unknown    ///< 未知或未定义的操作模式。
};

/**
 * @brief UI模态模式类型枚举。
 * 定义UI层可能进入的各种模态状态，用于通知hardware层调整相应行为。
 */
enum class UiModalType : uint8_t {
    None = 0,        ///< 无模态模式，正常状态。
    Dialog,          ///< 通用对话框模态（如设置对话框、确认对话框等）。
    Emergency,       ///< 紧急停止或紧急操作模态。
    Maintenance,     ///< 维护模式模态（如校准、维护向导等）。
    FileOperation,   ///< 文件操作模态（如程序加载、保存等）。
    ManualControl,   ///< 手动控制专用模态（如手动对刀、手动操作等）。
    SystemConfig,    ///< 系统配置模态（如参数设置、系统配置等）。
    ToolManagement,  ///< 刀具管理模态（如刀具设置、刀库操作等）。
    ProgramEdit,     ///< 程序编辑模态（如G代码编辑器打开）。
    Calibration,     ///< 校准操作模态（如轴校准、测头校准等）。
    DiagnosticTest,  ///< 诊断测试模态（如系统自检、硬件测试等）。
    CriticalError,   ///< 严重错误处理模态（需要用户介入的错误状态）。
    Unknown          ///< 未知或自定义的模态类型。
};

/**
 * @brief 刀库类型枚举。
 * 描述机床上安装的刀库的物理类型。
 */
enum class ToolChangerType : uint8_t {
    NONE = 0,  ///< 无刀库。
    CAROUSEL,  ///< 盘式/斗笠式刀库。
    CHAIN,     ///< 链式刀库。
    MANUAL,    ///< 手动换刀 (系统可能仍管理刀具数据，但换刀需人工操作)。
    Unknown    ///< 未知或未定义的刀库类型。
};

/**
 * @brief 刀具类型分类代码枚举。
 * 用于表示刀具的主要分类，实际刀具类型可能是此分类下的一个具体编号。
 */
enum class ToolCategory : int {
    UNKNOWN = 0,  ///< 未知或未分类刀具

    // --- 铣削类刀具 (通常编号范围 100-199) ---
    MILLING_GENERAL = 100,                      ///< 通用铣刀基类
    CYLINDRICAL_BALL_NOSE_MOLD_CUTTER = 110,    ///< 圆柱形球头模具铣刀
    CONICAL_BALL_NOSE_MOLD_CUTTER = 111,        ///< 圆锥形球头模具铣刀
    END_MILL = 120,                             ///< 立铣刀
    CHAMFERED_END_MILL = 121,                   ///< 带倒角立铣刀
    HORIZONTAL_MILLING_CUTTER = 130,            ///< 卧铣刀 (例如盘铣刀、侧铣刀)
    CHAMFERED_HORIZONTAL_MILLING_CUTTER = 131,  ///< 带倒角卧铣刀
    FACE_MILL = 140,                            ///< 面铣刀
    THREAD_MILL = 145,                          ///< 螺纹铣刀
    SLOTTING_MILL = 150,                        ///< 盘形铣刀/槽铣刀
    SAW_MILL = 151,                             ///< 锯片铣刀
    TAPERED_END_MILL = 155,                     ///< 截锥铣刀
    CHAMFERED_TAPERED_END_MILL = 156,           ///< 带倒角截锥铣刀
    CONICAL_MOLD_CUTTER = 157,                  ///< 圆锥形模具铣刀
    THREAD_DRILL_MILL = 160,                    ///< 螺纹钻铣刀（复合刀具）

    // --- 钻削类刀具 (通常编号范围 200-299) ---
    ACTUAL_TWIST_DRILL = 200,     ///< 麻花钻
    SOLID_DRILL_HEAD = 205,       ///< 整体钻头
    BORING_BAR_DRILL_TYPE = 210,  ///< 镗杆
    CENTER_DRILL_TYPE = 220,      ///< 中心钻
    POINTED_CHAMFER_DRILL = 230,  ///< 尖头倒角钻/锪钻
    FLAT_CHAMFER_DRILL = 231,     ///< 平头倒角钻/锪钻
    TAP = 240,                    ///< 螺纹丝锥 (通用)
    PRECISION_TAP = 241,          ///< 精密螺纹丝锥
    IMPERIAL_TAP = 242,           ///< 英制螺纹丝锥
    REAMER_TYPE = 250,            ///< 铰刀

    // --- 车削类刀具 (通常编号范围 500-599) ---
    ROUGHING_TURNING_TOOL = 500,      ///< 粗加工刀具
    FINISHING_TURNING_TOOL = 510,     ///< 精加工刀具
    GROOVING_PLUNGING_TOOL = 520,     ///< 切入刀具
    PARTING_TOOL = 530,               ///< 切断刀
    LATHE_THREADING_TOOL = 540,       ///< 螺纹车刀
    BUTTON_TOOL = 550,                ///< 钮扣刀具/圆刀片车刀
    ROTATING_DRILL_HEAD_LATHE = 560,  ///< 旋转钻头 (车床动力刀具)
    PROBE_3D_TURNING = 580,           ///< 3D测头 (车削)
    CALIBRATION_TOOL_TURNING = 585,   ///< 校准刀具 (车削)

    // --- 特种类刀具 (通常编号范围 700-900) ---
    T_SLOT_CUTTER = 700,             ///< T型槽铣刀/槽锯
    PROBE_3D = 710,                  ///< 3D测头 (通用)
    EDGE_FINDER_PROBE = 711,         ///< 寻边测头
    UNIDIRECTIONAL_PROBE = 712,      ///< 单向测头
    L_SHAPE_PROBE = 713,             ///< L形测头
    STAR_PROBE = 714,                ///< 星形测头
    CALIBRATION_TOOL_SPECIAL = 725,  ///< 校准刀具 (特种)
    STOPPER_FIXTURE = 730,           ///< 挡块/定位件
    TAILSTOCK_SLEEVE = 731,          ///< 顶尖套筒
    STEADY_REST = 732,               ///< 中心架
    AUXILIARY_TOOL = 900             ///< 辅助刀具 (通用类别)
};

/**
 * @brief 刀具寿命与使用计数数据结构体。
 */
struct ToolLifeData {
    double nominalLifeSeconds = 0.0;  ///< 额定寿命 (秒)。
    double usedLifeSeconds = 0.0;     ///< 已用寿命 (秒)。
    double warningLifeSeconds = 0.0;  ///< 寿命预警阈值 (秒) - 当已用寿命达到此值时预警。

    int32_t nominalUsageCount = 0;  ///< 额定使用次数/工件数。
    int32_t currentUsageCount = 0;  ///< 当前已用次数/工件数。
    int32_t warningUsageCount = 0;  ///< 次数/工件数预警阈值 - 当已用次数达到此值时预警。

    double currentWearValue = 0.0;  ///< 当前累计磨损量 (例如 mm)。
    double maxWearValue = 0.0;      ///< 最大允许磨损量 (寿命终点)。
    double warningWearValue = 0.0;  ///< 磨损预警阈值。
};

/**
 * @brief 单个螺距补偿点的数据结构
 */
struct PitchCompensationPoint {
    double position;       ///< 补偿点位置 (mm)。
    double positiveError;  ///< 正向移动时的误差补偿值 (0.1μm)。
    double negativeError;  ///< 负向移动时的误差补偿值 (0.1μm)。
};

/**
 * @brief 轴的螺距补偿配置
 */
struct AxisPitchCompensation {
    int axisIndex;         ///< 轴索引。
    bool isEnabled;        ///< 是否启用螺距补偿。
    bool isCyclicMode;     ///< 是否为循环补偿模式 (true) 或单次补偿模式 (false)。
    double startPosition;  ///< 补偿起始位置 (mm)。
    double endPosition;    ///< 补偿结束位置 (mm)。
    std::vector<PitchCompensationPoint> compensationPoints;  ///< 补偿点列表。
};

/**
 * @brief 刀具信息结构体。
 * 包含与刀具相关的各种静态和动态信息，如编号、偏置、几何尺寸、寿命等。
 */
struct ToolInfo {
    // --- 唯一标识符 ---
    std::string uuid;  ///< 全局唯一的内部标识符 (UUID)，用于可靠地跟踪刀具对象。

    // --- 基本识别信息 ---
    std::string name;        ///< 刀具名称 (例如 "10mm 立铣刀", "M8 丝锥")。
    int number = 0;          ///< 全局唯一的刀具号 (例如 T01 中的 1)。
    int toolChangerId = -1;  ///< 刀具当前所在刀库ID (-1 表示不在刀库或未知)。
    int pocket = 0;  ///< 在 toolChangerId 指定的刀库中的刀袋号/刀位号 (如果不在刀库则无意义)。

    // --- 刀具描述与分类 ---
    int toolTypeCode = 0;  ///< 刀具的数字类型代码。具体值应参考 ToolCategory
                           ///< 枚举定义的范围或更详细的系统规范。例如 101 代表立铣刀。
    int toolDirection = 0;  ///< 刀具方向 (1-右上，2-左上，3-左下，4-右下，5-右，6-上，7-左，8-下)。
    int numberOfFlutes = 0;    ///< 刀具刃数/齿数 (例如 2刃, 4刃)。
    int sisterToolNumber = 0;  ///< 姊妹刀具号，同类刀具从1开始依次递增。
    int dNumber = 0;           ///< 姊妹刀具号相同的同类刀具的刀沿号 (1-9)。

    // --- 主要几何参数与磨损 (对应传统 H 和 D 值的基础) ---
    double geometryLengthZ = 0.0;      ///< Z轴方向的几何长度 (H地址通常关联此值)。
    double geometryLengthZWear = 0.0;  ///< Z轴长度的磨损补偿。
    double geometryRadius = 0.0;       ///< XY平面内的几何半径 (D地址通常关联此值)。
    double geometryRadiusWear = 0.0;   ///< XY平面半径的磨损补偿。

    // --- 额外几何参数 (可选, 用于更详细定义或多轴补偿) ---
    double geometryLengthX = 0.0;      ///< X轴方向的几何长度/偏置 (若适用)。
    double geometryLengthXWear = 0.0;  ///< X轴长度的磨损补偿。
    double geometryLengthY = 0.0;      ///< Y轴方向的几何长度/偏置 (若适用)。
    double geometryLengthYWear = 0.0;  ///< Y轴长度的磨损补偿。
    double toolWidth = 0.0;            ///< 刀具宽度 (若适用, 例如切槽刀)。
    double toolLength = 0.0;           ///< 刀具长度 (若适用, 例如车刀)。
    double cuttingEdgeAngle = 0.0;     ///< 主偏角 (度)。
    double tipAngle = 0.0;             ///< 刀尖角度 (度, 例如钻头)。
    double noseRadius = 0.0;           ///< 刀尖圆角半径 (若适用)。

    // --- 当前激活的补偿值 (由CNC系统在运行时根据G代码和上述几何/磨损值计算并应用) ---
    double activeLengthOffset = 0.0;  ///< 当前在Z轴(或主轴方向)生效的总长度补偿值 (来自 G43/G44 Hxx)。
    double activeRadiusOffset = 0.0;  ///< 当前X轴在加工平面生效的总半径补偿值 (来自 G41/G42 Dxx)。

    // --- 状态与寿命 ---
    bool isActive = false;  ///< [A] 此刀具是否为当前通道活动刀具 (例如在主轴上或被程序最新选中)。
    bool isEnabled = true;              ///< [P] 刀具是否使能/可用 (与isDisabled相反或互补)。
    bool isMeasured = false;            ///< [M] 刀具是否已经过测量 (通常对刀后设置)。
    bool isLifeWarningReached = false;  ///< [W] 刀具寿命是否已达到预警值。
    bool isChanging = false;            ///< [C] 刀具当前是否正在进行换刀动作。
    bool isInFixedLocation = false;  ///< [L] 刀具是否位于固定位置 (例如特殊刀具，不可随意移动)。
    bool wasUsed = false;            ///< [U] 刀具是否曾经被使用过。
    ToolLifeData lifeData;           ///< 刀具寿命监控数据。

    // --- 主轴控制与冷却液设置 ---
    SpindleDirection spindleDirection = SpindleDirection::Stop;  ///< 主轴旋转方向设置 (停止/顺时针/逆时针)。
    bool coolant1Enabled = false;                                ///< 冷却液1开关状态 (M07/M09)。
    bool coolant2Enabled = false;                                ///< 冷却液2开关状态 (M08/M09)。

    // --- 有效性标志 ---
    bool isValid = false;  ///< [V] 刀具信息是否有效。
};

/**
 * @brief 多维点结构体，用于存储多轴坐标。
 * 通常用于表示机床坐标、工件坐标或相对坐标等。
 */
struct PointXD {
    /**
     * @brief 存储坐标的map，键为轴的整数索引，值为该轴的坐标值。
     * 轴索引对应于 SystemConfig::axesConfigs 中的顺序。
     */
    std::map<int, double> coordinates;

    /**
     * @brief 存储坐标的map，键为轴的名称，值为该轴的坐标值。
     */
     std::map<std::string, double> nameCoordinates;

    /**
     * @brief 将坐标转换为字符串表示
     * @return 格式化的坐标字符串，如 "0: 10.5, 1: 20.3"
     */
     std::string toString() const {
        std::string result;
        bool first = true;

        for (const auto& pair : coordinates) {
            if (!first) {
                result += ", ";
            }
            result += std::to_string(pair.first) + ": " + std::to_string(pair.second);
            first = false;
        }

        return result;
    }

     ///< 系统中是否存在X轴
    bool hasXAxis() {
        return nameCoordinates.count("X") > 0;
    }

    ///< 系统中是否存在Y轴
    bool hasYAxis() {
        return nameCoordinates.count("Y") > 0;
    }
    ///< 系统中是否存在Z轴
    bool hasZAxis() {
        return nameCoordinates.count("Z") > 0;
    }
};

/**
 * @brief 报警严重等级枚举。
 * 定义报警信息的不同严重程度，帮助用户判断报警的紧急性和影响。
 */
enum class AlarmSeverity : uint8_t {
    Info = 0,  ///< 信息: 提供状态信息或次要事件通知，通常不需要用户立即干预。
    Warning,  ///< 警告: 表示潜在问题或非关键错误，系统仍可运行，但建议用户注意或检查。
    Error,    ///< 错误:
            ///< 表示发生故障，可能影响当前操作或导致操作中止，通常可由用户清除或需要特定操作恢复。
    Critical  ///< 严重错误:
              ///< 表示严重系统故障，通常导致系统停机或主要功能不可用，可能需要重启或专业维修。
};

/**
 * @brief 报警信息结构体。
 * 包含单个报警的详细信息，如代码、消息、严重等级等。
 */
struct AlarmInfo {
    int code = 0;                                   ///< 报警代码 (控制器定义的内部代码或标准代码)。
    std::string message;                            ///< 报警信息文本描述。
    AlarmSeverity severity = AlarmSeverity::Error;  ///< 报警的严重等级，默认为错误级。
    std::chrono::system_clock::time_point timestamp;  ///< 报警发生的时间戳，如果未知或不适用，可以是 time_point{}。
    std::string source;  ///< 报警来源模块或组件的名称 (例如 "PLC", "NC", "AxisX_Drive", 可选，可能为空)。
    int channelId = -1;  ///< 报警所属的通道ID (-1 表示系统级报警或不适用于特定通道)。
};

/**
 * @brief 单段执行模式类型枚举。
 * 定义G代码程序单步执行的不同行为模式。
 */
enum class SingleBlockModeType : uint8_t {
    Off = 0,          ///< 单段模式关闭。
    SB1_Rough,        ///< 粗略单段执行 (标准程序块单步，对应传统单段)。
    SB2_Calculation,  ///< 计算程序段单步 (可能侧重于在逻辑和计算块处停止)。
    SB3_Precise       ///< 精准单段执行 (可能具有更细粒度的停止点或行为)。
};

/**
 * @brief 程序状态结构体。
 * 包含与G代码程序执行相关的各种动态状态信息。
 */
struct ProgramStatus {
    std::string programName;  ///< 程序名称。

    // --- 程序层级与行号信息 ---
    int32_t currentProgramNo = -1;  ///< 当前加工程序序号：-1表示主程序，0-n表示子程序编号。
    uint32_t currentBlock = 0;      ///< 当前主程序加工行号 (主程序中的绝对行号，1开始)。
    uint32_t currentLine = 0;  ///< 当前程序加工行号 (如果当前为主程序，则与currentBlock一致，1开始)。
    uint32_t totalLines = 0;    ///< 当前主程序文件总行数 (如果控制器能够提供)。
    uint32_t nestingLevel = 0;  ///< 程序嵌套调用层级：0为主程序层，1-n为子程序嵌套层级。
    int32_t loopCounter = 0;  ///< 当前程序段的循环执行次数 (例如 M99 P_ 循环计数，如果适用且可获取)。

    // --- 程序运行状态 ---
    bool isRunning = false;  ///< 程序是否在运行 (非暂停/停止/错误)。
    bool isPaused = false;   ///< 程序是否暂停。
    bool isError = false;    ///< 程序是否因错误停止。

    // --- 程序控制标志位 ---
    bool isOptionalStopActive = false;     ///< 选择性停止是否激活 (对应M01指令)。
    bool isHandwheelOffsetActive = false;  ///< 手轮偏移是否激活 (对应M01指令)。
    bool isBlockSkipActive = false;        ///< 程序段跳过是否激活 (对应G代码中的 '/' 符号)。
    bool isMstHoldActive = false;          ///< MST模式是否激活。
    // 按键支持SB3_Precise
    SingleBlockModeType currentSingleBlockMode = SingleBlockModeType::Off;  ///< 当前激活的单段模式类型。

    // 按键不支持
    bool isProgramTestActive = false;  ///< 程序测试模式是否激活 (PRT)。
    bool isDryRunActive = false;       ///< 空运行模式是否激活 (DRY)。
};

/**
 * @brief 轴配置结构体。
 * 包含单个逻辑轴的静态配置参数，这些参数通常在系统初始化时设定，并描述轴的物理特性和限制。
 */
struct CncAxisConfig {
    std::string name;                    ///< 轴名称 (例如 "X", "Y", "Z", "A", "SP1_ROT")。
    AxisType type = AxisType::Unknown;   ///< 轴类型 (线性/旋转)。
    double softwareLimitPositive = 0.0;  ///< 正向软件限位值 (单位由系统决定，mm 或 degree)。
    double softwareLimitNegative = 0.0;  ///< 负向软件限位值 (单位由系统决定，mm 或 degree)。
    double maxVelocity = 0.0;  ///< 轴最大允许速度 (单位/秒 或 单位/分钟, 具体由系统文档说明)。
    double maxAcceleration = 0.0;  ///< 轴最大允许加速度 (单位/秒^2, 具体由系统文档说明)。
    double maxJerk = 0.0;  ///< 轴最大允许加加速度/跃度 (单位/秒^3, 可选，0表示不使用或未知)。
    double pulseEquivalent = 1.0;  ///< 脉冲当量 (例如 mm/pulse 或 deg/pulse)，即每电机脉冲对应的物理位移。
    double backlashValue = 0.0;  ///< 反向间隙补偿值 (单位由系统决定，mm 或 degree, 可选，0表示无补偿或未知)。

    bool isDiameterProgramming = false; ///< true表示直径编程， false表示半径编程

    /**
     * @brief 获取轴的WCS。
     * @return WCS。
     */
     const double getWorkPosition(const double mcs, const double totalOffset) const {
        if (isDiameterProgramming) {
            return (mcs - totalOffset) * 2.0;
        } else {
            return mcs - totalOffset;
        }
    }

    /**
     * @brief 获取轴的MCS。
     * @return MCS。
     */
     const double getMachinePosition(const double wcs, const double totalOffset) const {
        if (isDiameterProgramming) {
            return wcs / 2.0 + totalOffset;
        } else {
            return wcs + totalOffset;
        }
    }

    /**
     * @brief 获取轴的刀具零偏。
     * @note 包括了刀具长度和刀具长度磨损的合计零偏。
     * @return 刀具零偏。
     */
    const double getToolZeroOffset(double geometryLength, double geometryLengthWear) const {
        if (isDiameterProgramming) {
            // 刀具长度 + 刀具长度磨损 / 2
            return geometryLength + geometryLengthWear / 2.0;
        } else {
            // 刀具长度 + 刀具长度磨损
            return geometryLength + geometryLengthWear;
        }
    }
};

/**
 * @brief 主轴配置结构体。
 * 包含单个主轴的静态配置参数，描述主轴的性能、特性和限制。
 */
struct SpindleConfig {
    std::string name;          ///< 主轴名称 (例如 "S", "S1", "MainSpindle")。
    double maxSpeedRpm = 0.0;  ///< 主轴最大转速 (RPM - Revolutions Per Minute)。
    double minSpeedRpm = 0.0;  ///< 主轴最小可控转速 (RPM, 可选, 0表示与最大转速之间连续可控或未知)。
    int numberOfGears = 1;  ///< 主轴齿轮数/档位数 (至少为1)。
    /**
     * @brief 主轴达到最大转速的平均加速度 (RPM/秒, 可选, 0表示未知或使用系统默认)。
     * @note 这可能是一个简化值，实际加减速可能是非线性的或分段的。
     */
    double accelerationRpmps = 0.0;
    /**
     * @brief 主轴从最大转速停止的平均减速度 (RPM/秒, 可选, 0表示未知或使用系统默认)。
     */
    double decelerationRpmps = 0.0;
    bool hasEncoder = false;     ///< 主轴是否配备编码器以进行闭环速度/位置反馈。
    bool isMainSpindle = false;  ///< 此主轴是否为系统的主主轴 (在多主轴系统中用于区分)。
};

/**
 * @brief 刀库配置结构体。
 * 包含单个刀库的静态配置参数，描述刀库的类型、容量和能力。
 */
struct ToolChangerConfig {
    std::string name;  ///< 刀库的名称或唯一标识符 (例如 "MainMagazine", "Carousel1")。
    ToolChangerType type = ToolChangerType::NONE;  ///< 刀库类型。
    int capacity = 0;                              ///< 刀库容量 (可容纳的刀具数量)。
    bool supportsRandomSelection = false;  ///< 是否支持随机选刀 (即T指令可以直接指定任意刀位号的刀具，而无需按顺序)。
    std::chrono::duration<double> averageChangeTime =
        std::chrono::duration<double>(0.0);  ///< 平均换刀时间 (秒，可选的参考性能指标，0表示未知)。
    double maxToolWeightKg = 0.0;  ///< 此刀库允许装载的最大刀具重量 (千克，可选，0表示无限制或未知)。
    double maxToolDiameterMm = 0.0;  ///< 此刀库允许装载的最大刀具直径 (毫米，可选，0表示无限制或未知)。
    /**
     * @brief 此刀库允许装载的最大刀具长度。
     * 通常指从主轴基准面算起的长度 (毫米，可选，0表示无限制或未知)。
     */
    double maxToolLengthMm = 0.0;
};

/**
 * @brief 用于描述单个当前活动的G功能及其状态的结构体。
 * 由 getActiveGFunctionList 方法返回，提供一个动态的活动G功能列表。
 */
struct ActiveGFunction {
    /**
     * @brief G功能组的描述性名称、标准G代码组的标识或控制器特定功能的名称。
     * 例如: "运动模式", "G_GROUP_01", "平面选择", "BRISK_LEVEL", "DYNNORM_STATUS"
     */
    std::string groupName;
    /**
     * @brief 该组当前激活的G代码 (例如 "G01"), 状态值 (例如 "ON", "OFF", "LEVEL2"),
     * 或具体的参数值 (例如 "G54", "H17", "D05").
     */
    std::string activeValue;
};

// --- 通道配置相关结构体 ---
/**
 * @brief 定义了单个轴在一个通道内的映射关系和本地名称。
 */
struct ChannelAxisMapping {
    int globalAxisIndex;  ///< 该通道使用的轴，在 SystemConfig::axesConfigs 中的全局索引。
    /**
     * @brief 该轴在本通道内的逻辑名称 (可选, 例如 "X", "Y", "C1")。
     * 如果为空，则通常直接使用全局轴名称。
     */
    std::string channelLocalName;
};

/**
 * @brief 定义了单个主轴在一个通道内的映射关系和本地名称。
 */
struct ChannelSpindleMapping {
    int globalSpindleIndex;  ///< 该通道使用的主轴，在 SystemConfig::spindlesConfigs 中的全局索引。
    /**
     * @brief 该主轴在本通道内的逻辑名称 (可选, 例如 "S", "S1", "MainSpindle")。
     * 如果为空，则通常直接使用全局主轴名称。
     */
    std::string channelLocalName;
};

/**
 * @brief 通道配置结构体。
 * 包含单个通道的静态配置参数，如名称以及其直接关联和控制的轴与主轴列表。
 */
struct ChannelConfig {
    std::string name;  ///< 通道的描述性名称 (例如 "MainChannel", "Turret1", "Path_A")。
    /**
     * @brief 此通道直接控制或使用的轴及其在本通道内的映射关系。
     * 定义了哪些全局轴属于这个通道以及它们在通道内的本地标识。
     */
    std::vector<ChannelAxisMapping> axes;
    /**
     * @brief 此通道直接控制或使用的主轴及其在本通道内的映射关系。
     * 定义了哪些全局主轴属于这个通道以及它们在通道内的本地标识。
     */
    std::vector<ChannelSpindleMapping> spindles;
};

/**
 * @brief 工件坐标系信息结构体
 */
struct WorkOffsetInfo {
    std::string name;         ///< 工件坐标系名称 (例如 "G54", "G55", "G54.1P12")
    int index = 0;            ///< 工件坐标系索引 (用于内部标识)
    bool isActive = false;    ///< 是否为当前激活的工件坐标系
    bool isReadOnly = false;  ///< 是否为只读坐标系
    std::string description;  ///< 描述信息 (可选)
};

/**
 * @brief 宏变量范围配置结构体。
 * 包含系统中可用宏变量范围的配置信息。
 *
 * 基于Googol SDK调查结果：
 * - 宏变量API访问范围：#500-#29999 (通过GTC_GetMacroVarValue/GTC_SetMacroVarValue函数)
 * - 可用范围由系统参数 m_nUserVarShareMinNo 到 m_nUserVarShareMaxNo 确定 (默认500-2999)
 */
struct MacroVariableRange {
    int minUserVarIndex = 500;  ///< 可用宏变量最小索引值 (对应Googol SDK的m_nUserVarShareMinNo，默认500)。
    int maxUserVarIndex = 29999;  ///< 可用宏变量最大索引值 (对应Googol SDK的m_nUserVarShareMaxNo，默认29999)。
};

/**
 * @brief 系统级配置信息结构体。
 * 聚合了CNC系统的各项静态配置、特性、能力计数以及详细的组件配置列表
 * (如通道、轴、主轴、刀库等)。通过 getSystemConfig() 方法获取，通常为只读信息，反映系统当前配置。
 */
struct SystemConfig {
    // 一、组件数量 (这些计数值应与对应配置列表的大小一致，通常为只读，由系统配置决定)
    /**
     * @brief 控制通道总数 (只读，由系统配置决定)。
     * 应等于 SystemConfig::channelsConfigs 向量的大小。
     */
    int numberOfChannels = 0;
    /**
     * @brief 系统中配置的逻辑轴总数 (只读，由系统配置决定)。
     * 应等于 SystemConfig::axesConfigs 向量的大小。
     */
    int totalNumberOfAxes = 0;
    /**
     * @brief 系统中配置的主轴总数 (只读，由系统配置决定)。
     * 应等于 SystemConfig::spindlesConfigs 向量的大小。
     */
    int totalNumberOfSpindles = 0;
    /**
     * @brief 刀库总数 (只读，由系统配置决定)。
     * 应等于 SystemConfig::toolChangersConfigs 向量的大小。
     */
    int numberOfToolChangers = 0;

    // 二、性能及默认参数
    /**
     * @brief 最大可编程进给速度 (例如 G01/G02/G03)。
     * 单位通常为 (距离单位/分钟) 或 (距离单位/秒), 取决于系统配置。
     */
    double maxProgrammedFeedrate = 0.0;
    /**
     * @brief 最大快移速度 (G00)。
     * 单位通常为 (距离单位/分钟) 或 (距离单位/秒)。
     */
    double maxRapidTraverseRate = 0.0;
    /**
     * @brief 系统默认或快移加速度。
     * 单位通常为 (距离单位/秒^2)。
     */
    double defaultAcceleration = 0.0;

    // 三、单位制
    DistanceUnit linearUnit = DistanceUnit::Unknown;  ///< 系统默认线性距离单位 (例如 mm, inch)。
    AngleUnit angularUnit = AngleUnit::Unknown;       ///< 系统默认角度单位 (例如 degree, radian)。

    // 四、控制器及版本信息
    std::string sdkVersion;                 ///< 此接口实现所依赖的底层SDK或库的版本信息。
    std::string controllerModel;            ///< CNC控制器型号 (例如 "Fanuc 0i-MD", "Siemens 840D SL", 可选)。
    std::string controllerFirmwareVersion;  ///< CNC控制器固件版本 (可选)。
    std::string controllerSerialNumber;     ///< CNC控制器序列号 (可选)。

    // 五、系统能力与限制 (可选，0表示未指定或无明确限制)
    int maxToolCompensationPairs = 0;  ///< 系统支持的最大刀具补偿对数量 (H地址和D地址的数量)。
    int maxWorkOffsets = 0;  ///< 系统支持的最大标准工件坐标系数量 (例如 G54-G59, G54.1 Px 等的总和)。
    uint64_t maxProgramSizeKb = 0;  ///< 系统允许加载的最大程序文件大小 (KB)。
    int maxBlockBufferSize = 0;     ///< 程序段预读缓冲区大小 (可缓存的程序块/行数)。

    // 六、宏变量配置信息
    MacroVariableRange macroVariableRange;  ///< 系统宏变量范围配置，描述可用的宏变量范围。

    // 七、详细组件配置列表
    std::vector<ChannelConfig> channelsConfigs;          ///< 系统中所有已配置通道的详细信息列表。

    std::vector<CncAxisConfig> axesConfigs;              ///< 系统中所有已配置轴的详细信息列表。
    std::map<std::string, CncAxisConfig> axisConfigMap;  ///< 轴名称到轴配置的映射。

    std::vector<SpindleConfig> spindlesConfigs;          ///< 系统中所有已配置主轴的详细信息列表。
    std::vector<ToolChangerConfig> toolChangersConfigs;  ///< 系统中所有已配置刀库的详细信息列表。
    std::vector<WorkOffsetInfo> supportedWorkOffsets;    ///< 系统支持的工件坐标系列表。

    // 八、轴的详细
    ///< 系统中是否存在X轴
    bool hasXAxis() {
        return axisConfigMap.count("X") > 0;
    }
    ///< X轴是否为直径编程
    bool xAxisIsDiameterProgramming() {
        if (hasXAxis()) {
            return axisConfigMap["X"].isDiameterProgramming;
        } else {
            return false;
        }
    }

    ///< 系统中是否存在Y轴
    bool hasYAxis() {
        return axisConfigMap.count("Y") > 0;
    }
    ///< 系统中是否存在Z轴
    bool hasZAxis() {
        return axisConfigMap.count("Z") > 0;
    }
};

/**
 * @brief 代码类型枚举。
 */
enum class GMotionCodeType : uint8_t {
    GCode_G00 = 0,  ///< G00		直线插补
    GCode_G01,      ///< G01		直线插补
    GCode_G02,      ///< G02		顺时针圆弧插补
    GCode_G03,      ///< G03		逆时针圆弧插补
    GCode_Unknown   ///< 未知或未指定类型的轴。
};

/**
 * @brief NC代码解释后的刀具运动信息。
 */
struct GMotion {
    PointXD start;     ///< 起点：绝对坐标
    PointXD target;    ///< 终点：绝对坐标
    PointXD arc;       ///< 圆心：绝对坐标，用于G02 & G03的圆弧插补
    double arcRadius;  ///< 圆的半径：用于G02 & G03的圆弧插补

    unsigned long mainFilePos;    ///< 主程序位置
    unsigned long mainLineNo;     ///< 主程序行号：	>=0
    unsigned long currentLineNo;  ///< 当前文件行号： >=0
    unsigned long objNumber;      ///< 指令号:       >=0
    unsigned long number;         ///< N指令号

    GMotionCodeType codeType;  ///< 指令类型：ENUM_NC_CMD_TYPE
    short fileNo;              ///< 程序号：-1主程序，0~40子程序
    short fileLevel;           ///< 程序嵌套调用层（0~n,0为主程序层）

    double feed;  ///< 进给速度:mm/min

    int plan;  ///< 0:G17, 1:G18, 2:G19
};

/**
 * @brief 刀库刀位状态结构体。
 * 描述刀库中单个刀位的状态信息。
 */
struct PocketStatus {
    int pocketNumber;        ///< 刀位号。
    bool isOccupied;         ///< 当前刀位是否被刀具占用。
    int toolNumberInPocket;  ///< 如果 isOccupied 为 true，则表示当前刀位上刀具的逻辑刀号
                             ///< (T号)；否则此值无意义 (例如可为0或-1)。
    bool isWorkingPosition;  ///< 当前刀位是否为加工位。
    bool isEnabled;          ///< 当前刀位是否为启用状态。
};

// --- 抽象 CNC 接口 ---

/**
 * @brief CNC接口类，定义了与CNC系统交互的抽象方法。
 *
 * 该接口提供了一套标准化的函数，用于控制和查询CNC系统的状态、配置、
 * 程序执行、坐标、进给、主轴、刀具、报警等信息。
 * 所有的操作都通过返回 ErrorCode 来指示成功或失败。
 * 许多函数都接受 channelId 参数以支持多通道CNC系统。
 *
 * @note 线程安全:
 * ICncInterface 接口的实现者必须保证其实现是线程安全的。
 * 调用方应该能够从多个线程并发地安全调用此接口的任何方法，
 * 而无需进行外部同步。
 *
 * @note 异步操作与事件通知:
 * 对于可能耗时的操作 (例如程序加载、启动、回零等)，可以考虑在未来版本中
 * 提供异步接口 (例如通过回调或std::future) 以避免阻塞调用线程。
 * 同样，一个通用的事件通知机制 (例如观察者模式) 可以用于向客户端
 * 推送系统状态变化、报警等异步事件。
 */
class ICncInterface {
   public:
    /**
     * @brief 虚析构函数。
     * 确保派生类的析构函数能够被正确调用。
     */
    virtual ~ICncInterface() = default;

    // --- 生命周期管理 ---
    /**
     * @brief 初始化CNC接口。
     *
     * 连接到CNC控制器，加载必要的配置，并使接口准备好进行操作。
     * 此方法应在调用其他接口函数之前首先调用。
     *
     * @param params 初始化参数结构体，包含配置路径、可写路径、日志接口等信息。
     *               如果某些参数为空或nullptr，实现应使用合理的默认值。
     * @return ErrorCode 指示操作成功或失败。
     * @see shutdown(), isInitialized(), CncInitializationParams
     *
     * @note 推荐的初始化流程：
     *       1. 验证和处理传入的参数
     *       2. 设置日志输出（如果提供了logger）
     *       3. 加载配置文件（如果提供了configPath）
     *       4. 建立与硬件的连接
     */
    virtual ErrorCode initialize(const CncInitializationParams& params) = 0;

    /**
     * @brief 关闭CNC接口，释放所有占用的资源。
     *
     * 断开与CNC控制器的连接，清理内部状态。
     * @return ErrorCode 指示操作成功或失败。
     * @see initialize()
     */
    virtual ErrorCode shutdown() = 0;

    /**
     * @brief 检查接口是否已成功初始化。
     * @return 如果接口已初始化，则为 true；否则为 false。
     * @see initialize()
     */
    virtual bool isInitialized() const = 0;

    // --- 配置参数读取 ---
    /**
     * @brief 获取系统级的各项静态配置、特性、能力以及详细的组件配置列表。
     *
     * 填充传入的 SystemConfig 结构体，该结构体包含了通道、轴、主轴、刀库等的配置信息，
     * 以及系统默认单位、性能参数、版本信息等。
     * @param[out] config 用于接收系统配置信息的 SystemConfig 结构体引用。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode getSystemConfig(SystemConfig& config) = 0;

    // --- 系统状态与模式 ---
    /**
     * @brief 获取 CNC 系统的总体宏观状态。
     *
     * 这通常是所有通道状态的聚合或一个最高级别的系统状态。
     * @param[out] state 用于接收系统状态的 CncSystemState 枚举引用。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode getSystemState(CncSystemState& state) = 0;

    /**
     * @brief 获取指定通道的当前操作模式。
     * @param channelId 目标通道的ID。
     * @param[out] mode 用于接收操作模式的 OperatingMode 枚举引用。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode getOperatingMode(int channelId, OperatingMode& mode) = 0;

    /**
     * @brief 设置指定通道的操作模式。
     * @note 并非所有操作模式都能通过SDK直接设置，或可能受限于当前系统状态。
     * @param channelId 目标通道的ID。
     * @param mode 要设置的操作模式。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode setOperatingMode(int channelId, OperatingMode mode) = 0;

    /**
     * @brief 获取接口实现层或底层SDK报告的最后一个错误信息文本。
     * @note 这通常是一个全局性的错误信息，可能不特定于某个通道。
     * @param[out] errorMsg 用于接收错误信息文本的字符串引用。
     * @return ErrorCode 指示操作成功或失败 (例如，如果没有错误信息可获取)。
     */
    virtual ErrorCode getLastError(std::string& errorMsg) = 0;

    /**
     * @brief 获取指定通道当前重要的活动G功能列表。
     *
     * 返回一个 ActiveGFunction
     * 对象的向量，每个对象描述一个活动的G代码模态或控制器特定功能及其状态。
     * @param channelId 目标通道的ID。
     * @param[out] functions 用于接收活动G功能列表的向量引用。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode getActiveGFunctionList(int channelId, std::vector<ActiveGFunction>& functions) = 0;

    // --- 坐标系与位置 --- (坐标单位由 SystemConfig.linearUnit/angularUnit 决定)
    // 所有与坐标相关的查询，除非另有说明，均针对指定的 channelId

    /**
     * @brief 获取指定通道所有映射轴的当前工件坐标 (PCS - Program Coordinate System)。
     * @param channelId 目标通道的ID。
     * @param[out] position 用于接收坐标信息的 PointXD 结构体引用。PointXD内部map的键为全局轴索引。
     * @param isReadFromCNC true从CNC读取，false自己计算
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode getWorkPosition(int channelId, PointXD& position, bool isReadFromCNC = false) = 0;

    /**
     * @brief 获取指定通道所有映射轴的当前机床坐标 (MCS - Machine Coordinate System)。
     * @param channelId 目标通道的ID。
     * @param[out] position 用于接收坐标信息的 PointXD 结构体引用。PointXD内部map的键为全局轴索引。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode getMachinePosition(int channelId, PointXD& position) = 0;
    /**
     * @brief 获取指定通道所有映射轴的当前相对坐标。
     * @param channelId 目标通道的ID。
     * @param[out] position 用于接收坐标信息的 PointXD 结构体引用。PointXD内部map的键为全局轴索引。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode getRelativePosition(int channelId, PointXD& position) = 0;

    /**
     * @brief 获取指定通道所有映射轴的剩余移动量 (distance-to-go)。
     * @param channelId 目标通道的ID。
     * @param[out] position 用于接收剩余移动量信息的 PointXD
     * 结构体引用。PointXD内部map的键为全局轴索引。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode getRemainingPosition(int channelId, PointXD& position) = 0;

    /**
     * @brief 获取指定通道下特定轴的当前工件坐标。
     * @param channelId 目标通道的ID。
     * @param axisIndex 目标轴在 SystemConfig::axesConfigs 中的全局索引。
     * @param[out] position 用于接收该轴工件坐标的 double 型引用。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode getAxisWorkPosition(int channelId, int axisIndex, double& position) = 0;

    /**
     * @brief 获取指定通道下特定轴的当前机床坐标。
     * @param channelId 目标通道的ID。
     * @param axisIndex 目标轴在 SystemConfig::axesConfigs 中的全局索引。
     * @param[out] position 用于接收该轴机床坐标的 double 型引用。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode getAxisMachinePosition(int channelId, int axisIndex, double& position) = 0;

    /**
     * @brief 获取指定通道下特定轴的全部零偏。
     * @param channelId 目标通道的ID。
     * @param axisIndex 目标轴在 SystemConfig::axesConfigs 中的全局索引。
     * @param[out] totalOffset 用于接收该轴全部零偏的 double 型引用。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode getAxisTotalOffset(int channelId, int axisIndex, double& totalOffset) = 0;

    /**
     * @brief 获取指定通道当前激活的工作坐标系名称 (例如 "G54", "G55")。
     * @param channelId 目标通道的ID。
     * @param[out] name 用于接收工作坐标系名称的字符串引用。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode getCurrentWorkOffsetName(int channelId, std::string& name) = 0;
    /**
     * @brief 获取指定通道当前激活的工作坐标系偏移值。
     * 该偏移值表示程序坐标系(PCS)原点在机床坐标系(MCS)中的位置。
     * @param channelId 目标通道的ID。
     * @param[out] offset 用于接收偏移值信息的 PointXD 结构体引用。PointXD内部map的键为全局轴索引。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode getCurrentWorkOffsetValue(int channelId, PointXD& offset) = 0;
    /**
     * @brief 获取指定通道特定名称的工件坐标系 (例如 "G54", "G55") 的偏移值。
     * 该偏移值表示程序坐标系(PCS)原点在机床坐标系(MCS)中的位置。
     * @param channelId 目标通道的ID。
     * @param workOffsetName 要获取的工件坐标系的名称 (例如 "G54")。
     * @param[out] offset 用于接收偏移值信息的 PointXD 结构体引用。PointXD内部map的键为全局轴索引。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode getWorkOffsetValue(int channelId, const std::string& workOffsetName, PointXD& offset) = 0;
    /**
     * @brief 为指定通道设置其特定名称的工件坐标系 (例如 "G54", "G55") 的偏移值。
     * @param channelId 目标通道的ID。
     * @param workOffsetName 要设置的工件坐标系的名称 (例如 "G54")。
     * @param offset 包含新偏移值的 PointXD 结构体。PointXD内部map的键应为全局轴索引。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode setWorkOffsetValue(int channelId, const std::string& workOffsetName, const PointXD& offset) = 0;

    // --- 进给与主轴 --- (均针对指定的 channelId)
    /**
     * @brief 获取指定通道的程序指令进给速度 (F 指令值)。
     * @param channelId 目标通道的ID。
     * @param[out] feedrate 用于接收指令进给速度的 double 型引用。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode getCommandFeedrate(int channelId, double& feedrate) = 0;
    /**
     * @brief 获取指定通道的实际合成轴运动速度。
     * @param channelId 目标通道的ID。
     * @param[out] feedrate 用于接收实际合成速度的 double 型引用。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode getActualFeedrate(int channelId, double& feedrate) = 0;
    /**
     * @brief 获取指定通道的当前进给倍率。
     * @param channelId 目标通道的ID。
     * @param[out] overridePercent 用于接收进给倍率的 double 型引用 (通常范围 [0, N]，表示百分比)。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode getFeedOverride(int channelId, double& overridePercent) = 0;
    /**
     * @brief 设置指定通道的进给倍率。
     * @param channelId 目标通道的ID。
     * @param overridePercent 要设置的进给倍率 (通常范围 [0, N]，表示百分比)。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode setFeedOverride(int channelId, double overridePercent) = 0;
    /**
     * @brief 获取指定通道的当前快移倍率 (G00)。
     * @param channelId 目标通道的ID。
     * @param[out] overridePercent 用于接收快移倍率的 double 型引用 (通常为百分比)。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode getRapidOverride(int channelId, double& overridePercent) = 0;
    /**
     * @brief 设置指定通道的快移倍率 (G00)。
     * @param channelId 目标通道的ID。
     * @param overridePercent 要设置的快移倍率 (通常为百分比)。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode setRapidOverride(int channelId, double overridePercent) = 0;
    /**
     * @brief 获取指定通道的特定主轴的程序指令主轴速度 (S 指令值, RPM)。
     * @param channelId 目标通道的ID。
     * @param spindleIndex 目标主轴在 SystemConfig::spindlesConfigs 中的全局索引。
     * @param[out] speedRpm 用于接收指令主轴速度的 double 型引用。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode getCommandSpindleSpeed(int channelId, int spindleIndex, double& speedRpm) = 0;
    /**
     * @brief 获取指定通道的特定主轴的实际主轴速度 (RPM)。
     * @param channelId 目标通道的ID。
     * @param spindleIndex 目标主轴在 SystemConfig::spindlesConfigs 中的全局索引。
     * @param[out] speedRpm 用于接收实际主轴速度的 double 型引用。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode getActualSpindleSpeed(int channelId, int spindleIndex, double& speedRpm) = 0;
    /**
     * @brief 获取指定通道的当前主轴倍率。
     * @note 假定主轴倍率是通道级的，影响该通道下所有活动主轴。
     * @param channelId 目标通道的ID。
     * @param[out] overridePercent 用于接收主轴倍率的 double 型引用 (通常为百分比)。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode getSpindleOverride(int channelId, double& overridePercent) = 0;
    /**
     * @brief 设置指定通道的主轴倍率。
     * @note 假定主轴倍率是通道级的，影响该通道下所有活动主轴。
     * @param channelId 目标通道的ID。
     * @param overridePercent 要设置的主轴倍率 (通常为百分比)。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode setSpindleOverride(int channelId, double overridePercent) = 0;
    /**
     * @brief 检查指定通道的特定主轴是否正在旋转。
     * 通常基于 M03/M04 状态或实际速度是否大于零。
     * @param channelId 目标通道的ID。
     * @param spindleIndex 目标主轴在 SystemConfig::spindlesConfigs 中的全局索引。
     * @param[out] isOn 如果主轴正在旋转，则为 true；否则为 false。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode isSpindleOn(int channelId, int spindleIndex, bool& isOn) = 0;

    // --- 刀库管理 (高级接口) ---
    /**
     * @brief 获取指定刀库中特定刀位上的刀具信息。
     * @param toolChangerId 目标刀库的ID (对应 SystemConfig::toolChangersConfigs
     * 中的索引或唯一标识)。
     * @param pocketNumber 目标刀库中的刀位号。
     * @param[out] toolInfo 用于接收刀具信息的 ToolInfo
     * 结构体引用。如果刀位为空或无效，toolInfo.isValid 应为 false。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode getToolInfoInPocket(int toolChangerId, int pocketNumber, ToolInfo& toolInfo) = 0;

    /**
     * @brief 将指定逻辑刀号的刀具加载到指定刀库的特定刀位。
     * 这通常意味着系统需要知道刀具号 toolNumber 对应的物理刀具，并执行物理加载动作。
     * @param toolChangerId 目标刀库的ID。
     * @param pocketNumber 目标刀库中的刀位号。
     * @param toolNumber 要加载的刀具的逻辑刀号 (T号)。
     * @return ErrorCode 指示操作成功或失败 (例如，刀位已被占用，刀具不存在，或物理操作失败)。
     */
    virtual ErrorCode loadToolIntoPocket(int toolChangerId, int pocketNumber, int toolNumber) = 0;

    /**
     * @brief 从指定刀库的特定刀位卸载刀具。
     * @param toolChangerId 目标刀库的ID。
     * @param pocketNumber 目标刀库中的刀位号。
     * @param[out] unloadedToolNumber 用于接收被卸载刀具的逻辑刀号
     * (T号)。如果刀位为空，此值可能为0或-1。
     * @return ErrorCode 指示操作成功或失败 (例如，刀位为空，或物理操作失败)。
     */
    virtual ErrorCode unloadToolFromPocket(int toolChangerId, int pocketNumber, int& unloadedToolNumber) = 0;

    /**
     * @brief 交换同一刀库中两个指定刀位上的刀具。
     * @param toolChangerId 目标刀库的ID。
     * @param pocketNumber1 第一个刀位的号。
     * @param pocketNumber2 第二个刀位的号。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode exchangeToolsInPockets(int toolChangerId, int pocketNumber1, int pocketNumber2) = 0;

    /**
     * @brief 将刀具从指定刀库的刀位移动到指定通道的主轴。
     * @param channelId 目标通道的ID，主轴属于此通道。
     * @param toolChangerId 刀具所在的刀库ID。
     * @param pocketNumber 刀具所在的刀库中的刀位号。
     * @param spindleIndex 目标主轴在 SystemConfig::spindlesConfigs 中的全局索引。
     * @return ErrorCode 指示操作成功或失败 (例如，主轴上已有刀具，刀位为空，或换刀动作失败)。
     */
    virtual ErrorCode moveToolFromPocketToSpindle(int channelId, int toolChangerId, int pocketNumber,
                                                  int spindleIndex) = 0;

    /**
     * @brief 将刀具从指定通道的主轴移动到指定刀库的刀位。
     * @param channelId 目标通道的ID，主轴属于此通道。
     * @param spindleIndex 刀具所在的主轴在 SystemConfig::spindlesConfigs 中的全局索引。
     * @param toolChangerId 目标刀库ID。
     * @param pocketNumber 目标刀库中的刀位号。
     * @param[out] movedToPocketToolNumber
     * 可选输出，如果刀库刀位原先有刀具被替换回刀库其他空位或临时存储，这里可以返回其刀号。
     *                                      如果只是简单放入空刀位，此参数可能不被使用或返回特定值。
     * @return ErrorCode 指示操作成功或失败
     * (例如，主轴上无刀具，刀位已被占用且无法自动处理，或换刀动作失败)。
     */
    virtual ErrorCode moveToolFromSpindleToPocket(int channelId, int spindleIndex, int toolChangerId, int pocketNumber,
                                                  int* movedToPocketToolNumber = nullptr) = 0;

    /**
     * @brief 获取指定刀库所有刀位的状态。
     * @param toolChangerId 目标刀库的ID。
     * @param[out] pocketStatuses 用于接收刀库中所有刀位状态的向量。
     *                            向量的顺序通常对应刀位号的顺序，或者 PocketStatus 结构体中包含
     * pocketNumber。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode getMagazineStatus(int toolChangerId, std::vector<PocketStatus>& pocketStatuses) = 0;

    /**
     * @brief 获取指定通道当前激活刀具的信息 (T, H, D等)。
     * 当前激活刀具是指在指定通道的主轴上，或程序最新选定的刀具。
     * @param channelId 目标通道的ID。
     * @param[out] toolInfo 用于接收刀具信息的 ToolInfo 结构体引用。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode getCurrentToolInfo(int channelId, ToolInfo& toolInfo) = 0;

    /**
     * @brief 根据 UUID 获取刀具参数。
     * 使用 UUID 作为唯一标识符，实现对单个刀具的精确、无歧义查找。
     * @param uuid 刀具的唯一标识符。
     * @param[out] toolInfo 用于接收刀具参数的 ToolInfo 结构体引用。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode getToolParameters(const std::string& uuid, ToolInfo& toolInfo) = 0;

    /**
     * @brief 设置或更新刀具参数。
     * UUID必须由调用者提供：若 UUID 存在则更新现有刀具；若 UUID 不存在则创建新刀具。
     * @param[in] toolInfo 包含刀具参数的结构体，UUID必须由调用者指定，不能为空。
     * @return ErrorCode 指示操作成功或失败。UUID为空时返回 InvalidParam。
     */
    virtual ErrorCode setToolParameters(const ToolInfo& toolInfo) = 0;

    /**
     * @brief 根据 UUID 删除刀具。
     * 使用 UUID 精确删除指定刀具，避免按刀号删除可能的歧义。
     * @param uuid 要删除的刀具的唯一标识符。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode deleteTool(const std::string& uuid) = 0;

    /**
     * @brief 获取所有有效刀具的参数信息。
     * 一次性获取系统中所有已定义的刀具参数，key 为 UUID 确保唯一性。
     * @param allToolsInfo 用于接收所有刀具信息的map，key为刀具UUID，value为刀具信息。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode getAllToolParameters(std::map<std::string, ToolInfo>& allToolsInfo) = 0;

    /**
     * @brief 获取指定刀库中所有刀具的完整信息。
     * 结合刀位状态和刀具参数，一次性获取刀库中所有刀具的完整信息。
     * @param toolChangerId 目标刀库的ID，-1表示获取不在任何刀库中的刀具（临时刀具）。
     * @param toolsInMagazine 用于接收刀库中刀具信息的向量。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode getToolsByMagazine(int toolChangerId, std::vector<ToolInfo>& toolsInMagazine) = 0;

    // --- 程序执行 --- (均针对指定的 channelId)
    /**
     * @brief 加载 G 代码程序文件到指定通道的控制器内存中。
     * @note 此操作可能耗时，未来可考虑异步版本。
     * @param channelId 目标通道的ID。
     * @param filePath 要加载的程序文件的完整路径。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode loadProgram(int channelId, const std::string& filePath) = 0;
    // --- 程序执行 --- (均针对指定的 channelId)
    /**
     * @brief 编译 G 代码程序文件到指定通道的控制器内存中，并返回编译后的运动指令。
     * @note 此操作可能耗时，未来可考虑异步版本。
     * @param channelId 目标通道的ID。
     * @param filePath 要加载的程序文件的完整路径。
     * @param[out] gMotions 用于接收编译后的运动指令的向量。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode compileProgram(int channelId, const std::string filePath, std::vector<GMotion>& gMotions) = 0;
    /**
     * @brief 从当前程序指针位置开始执行指定通道中已加载的程序。
     * @param channelId 目标通道的ID。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode startProgram(int channelId) = 0;
    /**
     * @brief 暂停指定通道当前正在执行的程序 (进给保持)。
     * @param channelId 目标通道的ID。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode pauseProgram(int channelId) = 0;
    /**
     * @brief 恢复指定通道已暂停的程序执行。
     * @param channelId 目标通道的ID。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode resumeProgram(int channelId) = 0;
    /**
     * @brief 停止指定通道当前正在执行的程序 (非复位)。
     * 通常会将程序指针保持在停止的位置。
     * @param channelId 目标通道的ID。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode stopProgram(int channelId) = 0;
    /**
     * @brief 复位指定通道的 NC 程序状态。
     * 通常会停止程序执行，清除模态状态，并将程序指针重置到程序开头。
     * @param channelId 目标通道的ID。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode resetProgram(int channelId) = 0;
    /**
     * @brief 获取指定通道的当前程序执行状态。
     * @param channelId 目标通道的ID。
     * @param[out] status 用于接收程序状态信息的 ProgramStatus 结构体引用。
     * @return ErrorCode 指示操作成功或失败。
     * @note ProgramStatus 结构体包含了如当前运行程序名、行号、块号、运行/暂停/错误状态等详细信息。
     */
    virtual ErrorCode getProgramStatus(int channelId, ProgramStatus& status) = 0;
    /**
     * @brief 在指定通道执行单条 MDI (Manual Data Input) 指令。
     * @note 此操作可能耗时，未来可考虑异步版本。
     * @param channelId 目标通道的ID。
     * @param mdiString 要执行的MDI指令字符串 (例如 "G00 X100 Y50")。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode executeMdi(int channelId, const std::string& mdiString) = 0;
    /**
     * @brief 在指定通道执行宏指令。
     * @note 此操作可能耗时，未来可考虑异步版本。
     * @param channelId 目标通道的ID。
     * @param mdiString 要执行的宏字符串 (例如 "G00 X100 Y50")。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode executeMacro(int channelId, const std::string& macro) = 0;
    /**
     * @brief 设置指定通道的程序段跳过 (/ 符号) 功能是否激活。
     * @param channelId 目标通道的ID。
     * @param enable true 表示激活，false 表示取消。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode setBlockSkip(int channelId, bool enable) = 0;
    /**
     * @brief 设置指定通道的选择性停止 (M01 指令) 功能是否激活。
     * @param channelId 目标通道的ID。
     * @param enable true 表示激活 (遇到M01程序会暂停)，false 表示取消 (M01被忽略)。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode setOptionalStop(int channelId, bool enable) = 0;
    /**
     * @brief 设置指定通道的单段执行模式。
     * @param channelId 目标通道的ID。
     * @param mode 要设置的单段模式类型 (SingleBlockModeType 枚举)。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode setSingleStepMode(int channelId, SingleBlockModeType mode) = 0;
    /**
     * @brief 设置指定通道的程序测试模式 (PRT) 是否激活。
     * @param channelId 目标通道的ID。
     * @param enable true 表示激活，false 表示取消。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode setProgramTestMode(int channelId, bool enable) = 0;
    /**
     * @brief 设置指定通道的空运行模式 (DRY) 是否激活。
     * @param channelId 目标通道的ID。
     * @param enable true 表示激活，false 表示取消。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode setDryRunMode(int channelId, bool enable) = 0;
    /**
     * @brief 设置指定通道的手轮运行模式 (DRF) 是否激活。
     * @param channelId 目标通道的ID。
     * @param enable true 表示激活，false 表示取消。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode setHandwheelMode(int channelId, bool enable) = 0;
    /**
     * @brief 读取指定通道的宏变量值 (#索引)。
     * @param channelId 目标通道的ID。
     * @param index 要读取的宏变量的索引号。
     * @param[out] value 用于接收宏变量值的 double 型引用。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode getMacroVariable(int channelId, int index, double& value) = 0;
    /**
     * @brief 写入指定通道的宏变量值 (#索引)。
     * @param channelId 目标通道的ID。
     * @param index 要写入的宏变量的索引号。
     * @param value 要写入的值。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode setMacroVariable(int channelId, int index, double value) = 0;

    // --- 手动操作 --- (假设手动操作是通道相关的，即一个通道处于手动模式时，其轴才能被手动操作)
    /**
     * @brief 开始指定通道下特定轴的点动 (JOG) 操作。
     * @param channelId 目标通道的ID。
     * @param axisIndex 目标轴在 SystemConfig::axesConfigs 中的全局索引。
     * @param speed 点动速度。正值表示正向，负值表示负向。单位通常为 (距离单位/分钟) 或
     * (角度单位/分钟)。
     * @param distance 点动距离。如果大于0，则为定长点动；如果小于等于0
     * (通常为0)，则为连续点动，直到调用 stopJog。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode startJog(int channelId, int axisIndex, double speed, double distance = 0.0) = 0;
    /**
     * @brief 停止指定通道下特定轴的点动操作。
     * @param channelId 目标通道的ID。
     * @param axisIndex 目标轴在 SystemConfig::axesConfigs 中的全局索引。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode stopJog(int channelId, int axisIndex) = 0;
    /**
     * @brief 设置或更新指定通道下特定轴的MPG/手轮控制状态和参数。
     * @param channelId 目标通道的ID。
     * @param axisIndex 目标轴在 SystemConfig::axesConfigs 中的全局索引。如果 active 为 true,
     * 此轴将被MPG控制。
     * @param incrementPerPulse MPG每单位脉冲对应的移动量 (例如 0.001, 0.01, 0.1 mm/pulse 或
     * deg/pulse)。
     * @param active true 表示激活MPG对此轴的控制，false 表示取消对此轴的MPG控制。
     *               通常一次只能有一个轴被MPG主动控制。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode setMpgControl(int channelId, int axisIndex, double incrementPerPulse, bool active) = 0;
    /**
     * @brief 获取指定通道当前MPG/手轮的控制状态。
     * @param channelId 目标通道的ID。
     * @param[out] controlledAxisIndex
     * 如果有轴被MPG控制，则返回该轴的全局索引；否则可能返回-1或特定值。
     * @param[out] currentIncrementPerPulse 返回当前MPG的每脉冲移动量。
     * @param[out] isAnyAxisMpgControlled 如果当前有任何轴被MPG控制，则为 true。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode getMpgControlStatus(int channelId, int& controlledAxisIndex, double& currentIncrementPerPulse,
                                          bool& isAnyAxisMpgControlled) = 0;

    // --- 螺距补偿管理 ---
    /**
     * @brief 获取指定通道和轴的螺距补偿配置。
     * @param channelId 目标通道的ID。
     * @param axisIndex 目标轴在 SystemConfig::axesConfigs 中的全局索引。
     * @param[out] compensation 用于接收螺距补偿配置的结构体引用。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode getPitchCompensation(int channelId, int axisIndex, AxisPitchCompensation& compensation) = 0;

    /**
     * @brief 设置指定通道和轴的螺距补偿配置。
     * @param channelId 目标通道的ID。
     * @param axisIndex 目标轴在 SystemConfig::axesConfigs 中的全局索引。
     * @param compensation 包含螺距补偿配置的结构体。
     * @return ErrorCode 指示操作成功或失败。
     * @note 此操作会立即生效，建议在系统空闲时调用。
     */
    virtual ErrorCode setPitchCompensation(int channelId, int axisIndex, const AxisPitchCompensation& compensation) = 0;

    /**
     * @brief 启用或禁用指定通道和轴的螺距补偿功能。
     * @param channelId 目标通道的ID。
     * @param axisIndex 目标轴在 SystemConfig::axesConfigs 中的全局索引。
     * @param enable true 表示启用，false 表示禁用。
     * @param isCyclicMode 螺距补偿模式：true 为循环补偿，false 为单次补偿。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode enablePitchCompensation(int channelId, int axisIndex, bool enable, bool isCyclicMode = false) = 0;

    /**
     * @brief 获取指定通道和轴的螺距补偿当前状态。
     * @param channelId 目标通道的ID。
     * @param axisIndex 目标轴在 SystemConfig::axesConfigs 中的全局索引。
     * @param[out] isEnabled 螺距补偿是否启用。
     * @param[out] isCyclicMode 是否为循环补偿模式。
     * @param[out] currentCompensation 当前生效的补偿值 (0.1μm)。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode getPitchCompensationStatus(int channelId, int axisIndex, bool& isEnabled, bool& isCyclicMode,
                                                 double& currentCompensation) = 0;

    /**
     * @brief 获取指定通道所有轴的螺距补偿配置。
     * @param channelId 目标通道的ID。
     * @param[out] allCompensations 用于接收所有轴螺距补偿配置的向量。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode getAllPitchCompensations(int channelId, std::vector<AxisPitchCompensation>& allCompensations) = 0;

    /**
     * @brief 批量设置指定通道多个轴的螺距补偿配置。
     * @param channelId 目标通道的ID。
     * @param compensations 包含多个轴螺距补偿配置的向量。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode setAllPitchCompensations(int channelId,
                                               const std::vector<AxisPitchCompensation>& compensations) = 0;

    // --- 报警处理 ---
    /**
     * @brief 获取当前所有活动报警的列表。
     * @note 报警信息通过 AlarmInfo::channelId 区分是通道级报警还是系统级报警 (-1)。
     * @param[out] alarms 用于接收活动报警列表的向量引用。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode getActiveAlarms(std::vector<AlarmInfo>& alarms) = 0;
    /**
     * @brief 清除可复位的报警。
     * @param channelId 要清除报警的目标通道ID。如果为 -1
     * (默认值)，则尝试清除所有通道的可清除报警或系统级报警。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode clearAlarms(int channelId = -1) = 0;

    // --- 回零操作 --- (假设回零可以按通道组织或针对特定通道的轴)
    /**
     * @brief 对指定通道下的指定轴或所有关联轴执行回零程序。
     * @note 此操作可能耗时，未来可考虑异步版本。
     * @param channelId 目标通道的ID。
     * @param axisIndices
     * 要执行回零的轴的全局索引列表。如果向量为空，则通常表示对该通道关联的所有轴执行回零。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode startHoming(int channelId, const std::vector<int>& axisIndices) = 0;
    /**
     * @brief 检查指定通道下的单个轴是否已回零。
     * @param channelId 目标通道的ID。
     * @param axisIndex 目标轴在 SystemConfig::axesConfigs 中的全局索引。
     * @param[out] isHomed 如果该轴已回零，则为 true；否则为 false。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode isAxisHomed(int channelId, int axisIndex, bool& isHomed) = 0;
    /**
     * @brief 检查指定通道关联的所有轴是否都已回零。
     * @param channelId 目标通道的ID。
     * @param[out] allHomed 如果所有关联轴都已回零，则为 true；否则为 false。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode areAllAxesHomed(int channelId, bool& allHomed) = 0;

    // --- 系统通用配置管理（上层应用通常不关心其具体意义，主要用于备份/恢复/诊断） ---
    /**
     * @brief 获取完整的系统配置结构，通常用于备份、诊断或特定配置工具。
     *
     * 返回一个表示整个系统可配置参数的树状结构。
     * 上层应用通常不需要直接解析此结构的具体内容，除非是专门的配置管理程序。
     * @param[out] rootCategories 用于接收配置分类根列表的向量引用。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode getConfiguration(std::vector<ConfigCategory>& rootCategories) = 0;
    /**
     * @brief 设置系统配置，通常用于从备份恢复或批量修改配置。
     *
     * @warning 错误地使用此接口可能导致系统不稳定或功能异常。慎用！
     * @param rootCategoriesToUpdate 包含要更新的配置分类的向量。
     * @return ErrorCode 指示操作成功或失败。
     */
    virtual ErrorCode setConfiguration(const std::vector<ConfigCategory>& rootCategoriesToUpdate) = 0;

    // --- 事件处理 ---
    /**
     * @brief 注册一个事件监听器以接收CNC系统事件。
     * 监听器将通过其 onCncEvent 方法接收事件通知。
     * @param listener 指向实现 ICncEventListener 的对象的指针。
     *                 调用方拥有监听器对象的所有权，并必须确保它在注册期间保持有效。
     *                 多次注册相同的监听器可能会被忽略或导致错误。
     * @return ErrorCode::Success 如果注册成功，否则返回错误代码。
     * @note 监听器通知可能会在单独的线程中发生。ICncEventListener::onCncEvent 的实现必须线程安全。
     * @see unregisterEventListener(), ICncEventListener, CncEvent
     */
    virtual ErrorCode registerEventListener(ICncEventListener* listener) = 0;

    /**
     * @brief 注销一个之前注册的事件监听器。
     * 注销后，监听器将不再接收CNC事件。
     * @param listener 指向要注销的监听器对象的指针。
     *                 如果监听器未曾注册，此操作可能会被忽略或返回错误。
     * @return ErrorCode::Success 如果注销成功，否则返回错误代码。
     * @see registerEventListener()
     */
    virtual ErrorCode unregisterEventListener(ICncEventListener* listener) = 0;

    // --- UI模态模式通知 ---
    /**
     * @brief 通知hardware层UI已进入模态模式（枚举版本）。
     *
     * 使用枚举类型的类型安全版本，推荐优先使用此版本。
     * @param modalType 模态模式类型枚举值。
     * @param modalId 可选的模态实例标识符，用于区分同类型的不同模态实例。
     * @return ErrorCode 指示操作成功或失败。
     * @note 每次进入模态模式都应该对应一次exitModalMode调用。
     * @see exitModalMode()
     */
    virtual ErrorCode enterModalMode(UiModalType modalType, const std::string& modalId = "") = 0;

    /**
     * @brief 通知hardware层UI已退出模态模式（枚举版本）。
     *
     * 使用枚举类型的类型安全版本，推荐优先使用此版本。
     * @param modalType 退出的模态模式类型枚举值，应与对应的enterModalMode调用匹配。
     * @param modalId 可选的模态实例标识符，应与对应的enterModalMode调用匹配。
     * @return ErrorCode 指示操作成功或失败。
     * @see enterModalMode()
     */
    virtual ErrorCode exitModalMode(UiModalType modalType, const std::string& modalId = "") = 0;
};
